import React, { useState, useEffect, useRef } from 'react';
import { 
  Eye, 
  Code, 
  Layers, 
  Move, 
  Square, 
  Type, 
  Image, 
  MousePointer, 
  Smartphone, 
  Tablet, 
  Monitor,
  Palette,
  Settings,
  Play,
  RefreshCw,
  Save,
  Undo,
  <PERSON>o,
  Copy,
  Trash2,
  Plus
} from 'lucide-react';
import '../styles/VisualEditor.css';

// Visual element interface
interface VisualElement {
  id: string;
  type: 'div' | 'span' | 'button' | 'input' | 'img' | 'text' | 'component';
  tagName: string;
  className?: string;
  styles: Record<string, string>;
  attributes: Record<string, string>;
  content?: string;
  children: VisualElement[];
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  metadata: {
    filePath: string;
    lineNumber: number;
    framework?: 'react' | 'vue' | 'svelte' | 'html';
  };
}

// Visual project interface
interface VisualProject {
  id: string;
  name: string;
  path: string;
  framework: 'react' | 'vue' | 'svelte' | 'html';
  components: VisualElement[];
  pages: any[];
  assets: any[];
}

// Editing modes
enum EditingMode {
  SELECT = 'select',
  EDIT = 'edit',
  INSERT = 'insert',
  RESIZE = 'resize',
  MOVE = 'move'
}

// Device presets
const DEVICE_PRESETS = [
  { name: 'Desktop', width: 1920, height: 1080, icon: Monitor },
  { name: 'Laptop', width: 1366, height: 768, icon: Monitor },
  { name: 'Tablet', width: 768, height: 1024, icon: Tablet },
  { name: 'Mobile', width: 375, height: 667, icon: Smartphone }
];

interface VisualEditorProps {
  isVisible: boolean;
  onClose: () => void;
  projectPath?: string;
}

const VisualEditor: React.FC<VisualEditorProps> = ({ isVisible, onClose, projectPath }) => {
  const [project, setProject] = useState<VisualProject | null>(null);
  const [selectedElement, setSelectedElement] = useState<VisualElement | null>(null);
  const [editingMode, setEditingMode] = useState<EditingMode>(EditingMode.SELECT);
  const [viewMode, setViewMode] = useState<'design' | 'code' | 'split'>('design');
  const [devicePreset, setDevicePreset] = useState(DEVICE_PRESETS[0]);
  const [zoom, setZoom] = useState(100);
  const [isLoading, setIsLoading] = useState(false);
  const [showLayers, setShowLayers] = useState(true);
  const [showProperties, setShowProperties] = useState(true);
  const canvasRef = useRef<HTMLDivElement>(null);
  const previewRef = useRef<HTMLIFrameElement>(null);

  // Load project when component mounts
  useEffect(() => {
    if (isVisible && projectPath) {
      loadProject();
    }
  }, [isVisible, projectPath]);

  /**
   * Load visual project
   */
  const loadProject = async () => {
    try {
      setIsLoading(true);
      
      // Check if project already exists
      const projects = await window.electron.visual.getAllProjects();
      let existingProject = projects.find((p: VisualProject) => p.path === projectPath);
      
      if (!existingProject) {
        // Create new visual project
        const framework = detectFramework(projectPath || '');
        existingProject = await window.electron.visual.createProject(
          `Visual Project ${Date.now()}`,
          projectPath || '',
          framework
        );
      }
      
      setProject(existingProject);
    } catch (error) {
      console.error('Failed to load visual project:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Detect framework from project path
   */
  const detectFramework = (path: string): 'react' | 'vue' | 'svelte' | 'html' => {
    // Simple framework detection based on common patterns
    if (path.includes('react') || path.includes('next')) return 'react';
    if (path.includes('vue') || path.includes('nuxt')) return 'vue';
    if (path.includes('svelte')) return 'svelte';
    return 'html';
  };

  /**
   * Handle element selection
   */
  const handleElementSelect = (element: VisualElement) => {
    setSelectedElement(element);
    setEditingMode(EditingMode.SELECT);
  };

  /**
   * Handle element style change
   */
  const handleStyleChange = (property: string, value: string) => {
    if (!selectedElement) return;

    const updatedElement = {
      ...selectedElement,
      styles: {
        ...selectedElement.styles,
        [property]: value
      }
    };

    setSelectedElement(updatedElement);
    
    // TODO: Update the actual element in the project and sync with code
  };

  /**
   * Handle element content change
   */
  const handleContentChange = (content: string) => {
    if (!selectedElement) return;

    const updatedElement = {
      ...selectedElement,
      content
    };

    setSelectedElement(updatedElement);
    
    // TODO: Update the actual element in the project and sync with code
  };

  /**
   * Add new element
   */
  const addElement = (type: VisualElement['type']) => {
    const newElement: VisualElement = {
      id: `elem_${Date.now()}`,
      type,
      tagName: type === 'text' ? 'p' : type,
      styles: {
        position: 'absolute',
        left: '50px',
        top: '50px',
        width: type === 'button' ? '120px' : '200px',
        height: type === 'input' ? '40px' : type === 'button' ? '40px' : '100px',
        backgroundColor: type === 'button' ? '#007bff' : 'transparent',
        color: type === 'button' ? 'white' : '#333',
        border: type === 'input' ? '1px solid #ccc' : 'none',
        borderRadius: type === 'button' || type === 'input' ? '4px' : '0',
        padding: type === 'button' || type === 'input' ? '8px 16px' : '0'
      },
      attributes: {},
      content: type === 'text' ? 'Sample Text' : type === 'button' ? 'Button' : '',
      children: [],
      position: { x: 50, y: 50, width: 200, height: 100 },
      metadata: {
        filePath: project?.path || '',
        lineNumber: 1,
        framework: project?.framework
      }
    };

    // TODO: Add element to project and update code
    setSelectedElement(newElement);
  };

  /**
   * Delete selected element
   */
  const deleteElement = () => {
    if (!selectedElement) return;
    
    // TODO: Remove element from project and update code
    setSelectedElement(null);
  };

  /**
   * Duplicate selected element
   */
  const duplicateElement = () => {
    if (!selectedElement) return;
    
    const duplicated = {
      ...selectedElement,
      id: `elem_${Date.now()}`,
      position: {
        ...selectedElement.position,
        x: selectedElement.position.x + 20,
        y: selectedElement.position.y + 20
      }
    };

    // TODO: Add duplicated element to project
    setSelectedElement(duplicated);
  };

  /**
   * Start live preview
   */
  const startLivePreview = async () => {
    try {
      // TODO: Start development server and open preview
      console.log('Starting live preview...');
    } catch (error) {
      console.error('Failed to start live preview:', error);
    }
  };

  /**
   * Save changes
   */
  const saveChanges = async () => {
    try {
      // TODO: Save visual changes back to code files
      console.log('Saving changes...');
    } catch (error) {
      console.error('Failed to save changes:', error);
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="visual-editor">
      {/* Header */}
      <div className="visual-editor-header">
        <div className="visual-editor-title">
          <Eye size={20} />
          <span>Visual Editor</span>
          {project && <span className="project-name">- {project.name}</span>}
        </div>

        <div className="visual-editor-toolbar">
          {/* View Mode Toggle */}
          <div className="toolbar-group">
            <button
              className={`toolbar-btn ${viewMode === 'design' ? 'active' : ''}`}
              onClick={() => setViewMode('design')}
              title="Design View"
            >
              <Eye size={16} />
            </button>
            <button
              className={`toolbar-btn ${viewMode === 'code' ? 'active' : ''}`}
              onClick={() => setViewMode('code')}
              title="Code View"
            >
              <Code size={16} />
            </button>
            <button
              className={`toolbar-btn ${viewMode === 'split' ? 'active' : ''}`}
              onClick={() => setViewMode('split')}
              title="Split View"
            >
              <Layers size={16} />
            </button>
          </div>

          {/* Device Presets */}
          <div className="toolbar-group">
            {DEVICE_PRESETS.map((preset) => {
              const IconComponent = preset.icon;
              return (
                <button
                  key={preset.name}
                  className={`toolbar-btn ${devicePreset.name === preset.name ? 'active' : ''}`}
                  onClick={() => setDevicePreset(preset)}
                  title={`${preset.name} (${preset.width}x${preset.height})`}
                >
                  <IconComponent size={16} />
                </button>
              );
            })}
          </div>

          {/* Zoom Control */}
          <div className="toolbar-group">
            <button
              className="toolbar-btn"
              onClick={() => setZoom(Math.max(25, zoom - 25))}
              title="Zoom Out"
            >
              -
            </button>
            <span className="zoom-level">{zoom}%</span>
            <button
              className="toolbar-btn"
              onClick={() => setZoom(Math.min(200, zoom + 25))}
              title="Zoom In"
            >
              +
            </button>
          </div>

          {/* Actions */}
          <div className="toolbar-group">
            <button className="toolbar-btn" onClick={saveChanges} title="Save">
              <Save size={16} />
            </button>
            <button className="toolbar-btn" title="Undo">
              <Undo size={16} />
            </button>
            <button className="toolbar-btn" title="Redo">
              <Redo size={16} />
            </button>
            <button className="toolbar-btn" onClick={startLivePreview} title="Live Preview">
              <Play size={16} />
            </button>
          </div>

          <button className="toolbar-btn close-btn" onClick={onClose} title="Close">
            ×
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="visual-editor-content">
        {/* Left Sidebar - Tools & Layers */}
        <div className="visual-editor-sidebar left">
          {/* Tools */}
          <div className="sidebar-section">
            <h3>Tools</h3>
            <div className="tool-grid">
              <button
                className={`tool-btn ${editingMode === EditingMode.SELECT ? 'active' : ''}`}
                onClick={() => setEditingMode(EditingMode.SELECT)}
                title="Select"
              >
                <MousePointer size={16} />
              </button>
              <button
                className={`tool-btn ${editingMode === EditingMode.MOVE ? 'active' : ''}`}
                onClick={() => setEditingMode(EditingMode.MOVE)}
                title="Move"
              >
                <Move size={16} />
              </button>
              <button
                className="tool-btn"
                onClick={() => addElement('div')}
                title="Add Container"
              >
                <Square size={16} />
              </button>
              <button
                className="tool-btn"
                onClick={() => addElement('text')}
                title="Add Text"
              >
                <Type size={16} />
              </button>
              <button
                className="tool-btn"
                onClick={() => addElement('button')}
                title="Add Button"
              >
                <Square size={16} />
              </button>
              <button
                className="tool-btn"
                onClick={() => addElement('input')}
                title="Add Input"
              >
                <Square size={16} />
              </button>
              <button
                className="tool-btn"
                onClick={() => addElement('img')}
                title="Add Image"
              >
                <Image size={16} />
              </button>
            </div>
          </div>

          {/* Layers */}
          {showLayers && (
            <div className="sidebar-section">
              <div className="section-header">
                <h3>Layers</h3>
                <button
                  className="section-toggle"
                  onClick={() => setShowLayers(false)}
                >
                  ×
                </button>
              </div>
              <div className="layers-list">
                {project?.components.map((element) => (
                  <div
                    key={element.id}
                    className={`layer-item ${selectedElement?.id === element.id ? 'selected' : ''}`}
                    onClick={() => handleElementSelect(element)}
                  >
                    <span className="layer-icon">
                      {element.type === 'text' ? <Type size={12} /> : <Square size={12} />}
                    </span>
                    <span className="layer-name">
                      {element.tagName} {element.className && `.${element.className}`}
                    </span>
                  </div>
                ))}
                
                {(!project?.components || project.components.length === 0) && (
                  <div className="layers-empty">
                    <p>No elements yet</p>
                    <p>Add elements using the tools above</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Canvas Area */}
        <div className="visual-editor-canvas">
          {isLoading ? (
            <div className="canvas-loading">
              <div className="loading-spinner">
                <div className="spinner-ring"></div>
              </div>
              <p>Loading visual project...</p>
            </div>
          ) : (
            <div className="canvas-container">
              <div className="canvas-header">
                <span className="canvas-title">
                  {devicePreset.name} ({devicePreset.width}x{devicePreset.height})
                </span>
                <div className="canvas-actions">
                  <button className="canvas-btn" title="Refresh">
                    <RefreshCw size={14} />
                  </button>
                  <button className="canvas-btn" title="Settings">
                    <Settings size={14} />
                  </button>
                </div>
              </div>
              
              <div
                ref={canvasRef}
                className="canvas"
                style={{
                  width: devicePreset.width,
                  height: devicePreset.height,
                  transform: `scale(${zoom / 100})`,
                  transformOrigin: 'top left'
                }}
              >
                {viewMode === 'design' || viewMode === 'split' ? (
                  <div className="design-view">
                    {/* Design canvas content */}
                    <div className="canvas-content">
                      {project?.components.map((element) => (
                        <div
                          key={element.id}
                          className={`canvas-element ${selectedElement?.id === element.id ? 'selected' : ''}`}
                          style={{
                            ...element.styles,
                            position: 'absolute',
                            left: element.position.x,
                            top: element.position.y,
                            width: element.position.width,
                            height: element.position.height
                          }}
                          onClick={() => handleElementSelect(element)}
                        >
                          {element.content}
                        </div>
                      ))}
                      
                      {(!project?.components || project.components.length === 0) && (
                        <div className="canvas-empty">
                          <h3>Start Building</h3>
                          <p>Use the tools on the left to add elements to your design</p>
                          <button
                            className="btn btn-primary"
                            onClick={() => addElement('div')}
                          >
                            <Plus size={16} />
                            Add First Element
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ) : null}

                {viewMode === 'code' || viewMode === 'split' ? (
                  <div className="code-view">
                    {/* Code editor would go here */}
                    <div className="code-placeholder">
                      <Code size={48} />
                      <h3>Code View</h3>
                      <p>Code editor integration coming soon</p>
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          )}
        </div>

        {/* Right Sidebar - Properties */}
        {showProperties && selectedElement && (
          <div className="visual-editor-sidebar right">
            <div className="sidebar-section">
              <div className="section-header">
                <h3>Properties</h3>
                <div className="element-actions">
                  <button
                    className="element-action"
                    onClick={duplicateElement}
                    title="Duplicate"
                  >
                    <Copy size={12} />
                  </button>
                  <button
                    className="element-action"
                    onClick={deleteElement}
                    title="Delete"
                  >
                    <Trash2 size={12} />
                  </button>
                  <button
                    className="section-toggle"
                    onClick={() => setShowProperties(false)}
                  >
                    ×
                  </button>
                </div>
              </div>

              <div className="properties-content">
                {/* Element Info */}
                <div className="property-group">
                  <h4>Element</h4>
                  <div className="property-item">
                    <label>Tag:</label>
                    <span>{selectedElement.tagName}</span>
                  </div>
                  <div className="property-item">
                    <label>Type:</label>
                    <span>{selectedElement.type}</span>
                  </div>
                </div>

                {/* Content */}
                {selectedElement.type === 'text' || selectedElement.type === 'button' ? (
                  <div className="property-group">
                    <h4>Content</h4>
                    <textarea
                      value={selectedElement.content || ''}
                      onChange={(e) => handleContentChange(e.target.value)}
                      placeholder="Enter content..."
                      rows={3}
                    />
                  </div>
                ) : null}

                {/* Styles */}
                <div className="property-group">
                  <h4>Styles</h4>
                  
                  {/* Position */}
                  <div className="style-section">
                    <h5>Position</h5>
                    <div className="style-grid">
                      <div className="style-item">
                        <label>Left:</label>
                        <input
                          type="text"
                          value={selectedElement.styles.left || ''}
                          onChange={(e) => handleStyleChange('left', e.target.value)}
                        />
                      </div>
                      <div className="style-item">
                        <label>Top:</label>
                        <input
                          type="text"
                          value={selectedElement.styles.top || ''}
                          onChange={(e) => handleStyleChange('top', e.target.value)}
                        />
                      </div>
                      <div className="style-item">
                        <label>Width:</label>
                        <input
                          type="text"
                          value={selectedElement.styles.width || ''}
                          onChange={(e) => handleStyleChange('width', e.target.value)}
                        />
                      </div>
                      <div className="style-item">
                        <label>Height:</label>
                        <input
                          type="text"
                          value={selectedElement.styles.height || ''}
                          onChange={(e) => handleStyleChange('height', e.target.value)}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Appearance */}
                  <div className="style-section">
                    <h5>Appearance</h5>
                    <div className="style-grid">
                      <div className="style-item">
                        <label>Background:</label>
                        <input
                          type="color"
                          value={selectedElement.styles.backgroundColor || '#ffffff'}
                          onChange={(e) => handleStyleChange('backgroundColor', e.target.value)}
                        />
                      </div>
                      <div className="style-item">
                        <label>Color:</label>
                        <input
                          type="color"
                          value={selectedElement.styles.color || '#000000'}
                          onChange={(e) => handleStyleChange('color', e.target.value)}
                        />
                      </div>
                      <div className="style-item">
                        <label>Border:</label>
                        <input
                          type="text"
                          value={selectedElement.styles.border || ''}
                          onChange={(e) => handleStyleChange('border', e.target.value)}
                          placeholder="1px solid #ccc"
                        />
                      </div>
                      <div className="style-item">
                        <label>Border Radius:</label>
                        <input
                          type="text"
                          value={selectedElement.styles.borderRadius || ''}
                          onChange={(e) => handleStyleChange('borderRadius', e.target.value)}
                          placeholder="4px"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Typography */}
                  {(selectedElement.type === 'text' || selectedElement.type === 'button') && (
                    <div className="style-section">
                      <h5>Typography</h5>
                      <div className="style-grid">
                        <div className="style-item">
                          <label>Font Size:</label>
                          <input
                            type="text"
                            value={selectedElement.styles.fontSize || ''}
                            onChange={(e) => handleStyleChange('fontSize', e.target.value)}
                            placeholder="16px"
                          />
                        </div>
                        <div className="style-item">
                          <label>Font Weight:</label>
                          <select
                            value={selectedElement.styles.fontWeight || 'normal'}
                            onChange={(e) => handleStyleChange('fontWeight', e.target.value)}
                          >
                            <option value="normal">Normal</option>
                            <option value="bold">Bold</option>
                            <option value="lighter">Lighter</option>
                            <option value="bolder">Bolder</option>
                          </select>
                        </div>
                        <div className="style-item">
                          <label>Text Align:</label>
                          <select
                            value={selectedElement.styles.textAlign || 'left'}
                            onChange={(e) => handleStyleChange('textAlign', e.target.value)}
                          >
                            <option value="left">Left</option>
                            <option value="center">Center</option>
                            <option value="right">Right</option>
                            <option value="justify">Justify</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VisualEditor;
