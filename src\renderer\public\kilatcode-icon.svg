<svg xmlns="http://www.w3.org/2000/svg" width="256" height="256" viewBox="0 0 256 256">
  <defs>
    <linearGradient id="lightning-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0099cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00ffff;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <pattern id="batik-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="5" cy="5" r="1" fill="#00d4ff" opacity="0.1"/>
      <circle cx="15" cy="15" r="0.5" fill="#00ffff" opacity="0.1"/>
      <circle cx="10" cy="10" r="0.8" fill="#0099cc" opacity="0.1"/>
    </pattern>
  </defs>
  
  <!-- Background circle with batik pattern -->
  <circle cx="128" cy="128" r="120" fill="#0a0a0a" stroke="url(#lightning-gradient)" stroke-width="4"/>
  <circle cx="128" cy="128" r="116" fill="url(#batik-pattern)"/>
  
  <!-- Lightning bolt -->
  <path d="M 100 60 L 140 60 L 120 120 L 160 120 L 140 196 L 100 196 L 120 136 L 80 136 Z" 
        fill="url(#lightning-gradient)" 
        filter="url(#glow)"
        stroke="#00d4ff" 
        stroke-width="2"/>
  
  <!-- Inner glow effect -->
  <path d="M 100 60 L 140 60 L 120 120 L 160 120 L 140 196 L 100 196 L 120 136 L 80 136 Z" 
        fill="none" 
        stroke="#00ffff" 
        stroke-width="1" 
        opacity="0.8"/>
</svg>
