import React, { useState } from 'react';
import FileExplorer from '../FileExplorer';
import '../../styles/Sidebar.css';

interface SidebarProps {
  projectPath: string;
}

type SidebarTab = 'explorer' | 'search' | 'git' | 'extensions' | 'ai';

const Sidebar: React.FC<SidebarProps> = ({ projectPath }) => {
  const [activeTab, setActiveTab] = useState<SidebarTab>('explorer');
  
  const tabs = [
    {
      id: 'explorer' as SidebarTab,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
        </svg>
      ),
      title: 'Explorer'
    },
    {
      id: 'search' as SidebarTab,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
      ),
      title: 'Search'
    },
    {
      id: 'git' as SidebarTab,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"/>
        </svg>
      ),
      title: 'Source Control'
    },
    {
      id: 'extensions' as SidebarTab,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"/>
          <line x1="16" y1="8" x2="2" y2="22"/>
          <line x1="17.5" y1="15" x2="9" y2="15"/>
        </svg>
      ),
      title: 'Extensions'
    },
    {
      id: 'ai' as SidebarTab,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
        </svg>
      ),
      title: 'Mbah Ai'
    }
  ];
  
  const renderTabContent = () => {
    switch (activeTab) {
      case 'explorer':
        return <FileExplorer projectPath={projectPath} />;
      case 'search':
        return (
          <div className="tab-content">
            <div className="tab-header">
              <h3>Search</h3>
            </div>
            <div className="search-container">
              <input 
                type="text" 
                placeholder="Search files..." 
                className="search-input"
              />
              <div className="search-options">
                <label>
                  <input type="checkbox" />
                  Match Case
                </label>
                <label>
                  <input type="checkbox" />
                  Whole Word
                </label>
                <label>
                  <input type="checkbox" />
                  Use Regex
                </label>
              </div>
            </div>
          </div>
        );
      case 'git':
        return (
          <div className="tab-content">
            <div className="tab-header">
              <h3>Source Control</h3>
            </div>
            <div className="git-status">
              <p>No changes detected</p>
            </div>
          </div>
        );
      case 'extensions':
        return (
          <div className="tab-content">
            <div className="tab-header">
              <h3>Extensions</h3>
            </div>
            <div className="extensions-list">
              <p>Extensions marketplace coming soon...</p>
            </div>
          </div>
        );
      case 'ai':
        return (
          <div className="tab-content">
            <div className="tab-header">
              <h3>Mbah Ai Assistant</h3>
            </div>
            <div className="ai-chat">
              <div className="ai-welcome">
                <div className="ai-avatar">🤖</div>
                <p>Halo! Saya Mbah Ai, asisten coding Anda. Bagaimana saya bisa membantu?</p>
              </div>
              <div className="ai-input">
                <input 
                  type="text" 
                  placeholder="Tanya Mbah Ai..." 
                  className="ai-input-field"
                />
                <button className="ai-send-btn">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <line x1="22" y1="2" x2="11" y2="13"/>
                    <polygon points="22,2 15,22 11,13 2,9 22,2"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="sidebar">
      <div className="sidebar-tabs">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`sidebar-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
            title={tab.title}
          >
            {tab.icon}
          </button>
        ))}
      </div>
      
      <div className="sidebar-content">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default Sidebar;
