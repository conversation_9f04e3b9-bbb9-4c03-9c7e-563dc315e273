import { useMemo } from 'react';
import { 
  FileText, 
  FolderOpen, 
  Save, 
  Terminal, 
  Package, 
  Settings, 
  Search, 
  RefreshCw,
  Zap,
  GitBranch,
  Bug,
  Play,
  Palette,
  Code,
  Globe,
  Database
} from 'lucide-react';
import { Command } from '../components/CommandPalette';
import { useFileOperations } from './useFileOperations';
import { useEditorStore } from '../store/editorStore';

export const useCommands = () => {
  const { openFileDialog, createNewFile, saveFileDialog } = useFileOperations();
  const { tabs, activeTabId, saveTab, closeTab, closeAllTabs } = useEditorStore();

  const commands: Command[] = useMemo(() => [
    // File Commands
    {
      id: 'file.new',
      title: 'New File',
      description: 'Create a new file',
      category: 'File',
      icon: <FileText size={16} />,
      shortcut: 'Ctrl+N',
      action: () => createNewFile(),
      keywords: ['new', 'create', 'file']
    },
    {
      id: 'file.open',
      title: 'Open File',
      description: 'Open an existing file',
      category: 'File',
      icon: <FolderOpen size={16} />,
      shortcut: 'Ctrl+O',
      action: () => openFileDialog(),
      keywords: ['open', 'file', 'browse']
    },
    {
      id: 'file.save',
      title: 'Save File',
      description: 'Save the current file',
      category: 'File',
      icon: <Save size={16} />,
      shortcut: 'Ctrl+S',
      action: async () => {
        if (activeTabId) {
          await saveTab(activeTabId);
        }
      },
      keywords: ['save', 'file']
    },
    {
      id: 'file.save-as',
      title: 'Save As',
      description: 'Save the current file with a new name',
      category: 'File',
      icon: <Save size={16} />,
      shortcut: 'Ctrl+Shift+S',
      action: async () => {
        const activeTab = tabs.find(tab => tab.id === activeTabId);
        if (activeTab) {
          const result = await saveFileDialog(activeTab.content, activeTab.title);
          if (result.success && result.filePath) {
            // TODO: Update tab with new file path
          }
        }
      },
      keywords: ['save', 'as', 'file', 'new', 'name']
    },
    {
      id: 'file.close',
      title: 'Close File',
      description: 'Close the current file',
      category: 'File',
      icon: <FileText size={16} />,
      shortcut: 'Ctrl+W',
      action: () => {
        if (activeTabId) {
          closeTab(activeTabId);
        }
      },
      keywords: ['close', 'file']
    },
    {
      id: 'file.close-all',
      title: 'Close All Files',
      description: 'Close all open files',
      category: 'File',
      icon: <FileText size={16} />,
      shortcut: 'Ctrl+Shift+W',
      action: () => closeAllTabs(),
      keywords: ['close', 'all', 'files']
    },

    // Terminal Commands
    {
      id: 'terminal.new',
      title: 'New Terminal',
      description: 'Open a new terminal',
      category: 'Terminal',
      icon: <Terminal size={16} />,
      shortcut: 'Ctrl+Shift+`',
      action: async () => {
        await window.electron.terminal.create();
      },
      keywords: ['terminal', 'new', 'shell', 'command']
    },
    {
      id: 'terminal.toggle',
      title: 'Toggle Terminal',
      description: 'Show or hide the terminal panel',
      category: 'Terminal',
      icon: <Terminal size={16} />,
      shortcut: 'Ctrl+`',
      action: () => {
        // TODO: Implement terminal toggle
        console.log('Toggle terminal');
      },
      keywords: ['terminal', 'toggle', 'show', 'hide']
    },

    // Extension Commands
    {
      id: 'extensions.show',
      title: 'Show Extensions',
      description: 'Open the extension manager',
      category: 'Extensions',
      icon: <Package size={16} />,
      shortcut: 'Ctrl+Shift+X',
      action: () => {
        // TODO: Implement extension manager toggle
        console.log('Show extensions');
      },
      keywords: ['extensions', 'plugins', 'marketplace']
    },
    {
      id: 'extensions.install',
      title: 'Install Extension',
      description: 'Install an extension from file',
      category: 'Extensions',
      icon: <Package size={16} />,
      action: async () => {
        const result = await window.electron.dialog.openFile({
          filters: [
            { name: 'Extension Files', extensions: ['vsix', 'koix'] }
          ]
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
          await window.electron.extensions.install(result.filePaths[0]);
        }
      },
      keywords: ['install', 'extension', 'plugin', 'vsix', 'koix']
    },

    // View Commands
    {
      id: 'view.command-palette',
      title: 'Command Palette',
      description: 'Show all commands',
      category: 'View',
      icon: <Search size={16} />,
      shortcut: 'Ctrl+Shift+P',
      action: () => {
        // This command opens the palette itself, so it's handled differently
      },
      keywords: ['command', 'palette', 'search', 'commands']
    },
    {
      id: 'view.reload',
      title: 'Reload Window',
      description: 'Reload the current window',
      category: 'View',
      icon: <RefreshCw size={16} />,
      shortcut: 'Ctrl+R',
      action: () => {
        window.location.reload();
      },
      keywords: ['reload', 'refresh', 'window']
    },
    {
      id: 'view.toggle-dev-tools',
      title: 'Toggle Developer Tools',
      description: 'Open or close developer tools',
      category: 'View',
      icon: <Code size={16} />,
      shortcut: 'F12',
      action: () => {
        window.electron.ipc.send('toggle-dev-tools');
      },
      keywords: ['developer', 'tools', 'debug', 'inspect']
    },

    // Settings Commands
    {
      id: 'settings.open',
      title: 'Open Settings',
      description: 'Open application settings',
      category: 'Settings',
      icon: <Settings size={16} />,
      shortcut: 'Ctrl+,',
      action: () => {
        // TODO: Implement settings panel
        console.log('Open settings');
      },
      keywords: ['settings', 'preferences', 'configuration']
    },
    {
      id: 'settings.theme',
      title: 'Change Theme',
      description: 'Switch between available themes',
      category: 'Settings',
      icon: <Palette size={16} />,
      action: () => {
        // TODO: Implement theme selector
        console.log('Change theme');
      },
      keywords: ['theme', 'appearance', 'color', 'dark', 'light']
    },

    // Git Commands
    {
      id: 'git.init',
      title: 'Initialize Repository',
      description: 'Initialize a new Git repository',
      category: 'Git',
      icon: <GitBranch size={16} />,
      action: () => {
        // TODO: Implement git init
        console.log('Git init');
      },
      keywords: ['git', 'init', 'repository', 'version', 'control']
    },
    {
      id: 'git.status',
      title: 'Git Status',
      description: 'Show Git repository status',
      category: 'Git',
      icon: <GitBranch size={16} />,
      action: () => {
        // TODO: Implement git status
        console.log('Git status');
      },
      keywords: ['git', 'status', 'changes', 'repository']
    },

    // Debug Commands
    {
      id: 'debug.start',
      title: 'Start Debugging',
      description: 'Start debugging the current file',
      category: 'Debug',
      icon: <Bug size={16} />,
      shortcut: 'F5',
      action: () => {
        // TODO: Implement debugger
        console.log('Start debugging');
      },
      keywords: ['debug', 'start', 'breakpoint', 'run']
    },
    {
      id: 'debug.stop',
      title: 'Stop Debugging',
      description: 'Stop the current debugging session',
      category: 'Debug',
      icon: <Bug size={16} />,
      shortcut: 'Shift+F5',
      action: () => {
        // TODO: Implement debugger stop
        console.log('Stop debugging');
      },
      keywords: ['debug', 'stop', 'end', 'terminate']
    },

    // Run Commands
    {
      id: 'run.file',
      title: 'Run File',
      description: 'Run the current file',
      category: 'Run',
      icon: <Play size={16} />,
      shortcut: 'Ctrl+F5',
      action: () => {
        // TODO: Implement file runner
        console.log('Run file');
      },
      keywords: ['run', 'execute', 'file', 'script']
    },

    // Web Commands
    {
      id: 'web.preview',
      title: 'Live Preview',
      description: 'Start live preview for web projects',
      category: 'Web',
      icon: <Globe size={16} />,
      action: () => {
        // TODO: Implement live preview
        console.log('Live preview');
      },
      keywords: ['preview', 'live', 'web', 'browser', 'server']
    },
    {
      id: 'web.api-client',
      title: 'API Client',
      description: 'Open the REST API client',
      category: 'Web',
      icon: <Database size={16} />,
      action: () => {
        // TODO: Implement API client
        console.log('API client');
      },
      keywords: ['api', 'rest', 'client', 'http', 'request']
    },

    // AI Commands
    {
      id: 'ai.assistant',
      title: 'Mbah Ai Assistant',
      description: 'Open AI assistant for coding help',
      category: 'AI',
      icon: <Zap size={16} />,
      shortcut: 'Ctrl+Shift+A',
      action: () => {
        // TODO: Implement AI assistant
        console.log('Mbah Ai assistant');
      },
      keywords: ['ai', 'assistant', 'mbah', 'help', 'coding', 'autocomplete']
    },
    {
      id: 'ai.generate-tests',
      title: 'Generate Tests',
      description: 'Generate unit tests for current file',
      category: 'AI',
      icon: <Zap size={16} />,
      action: () => {
        // TODO: Implement test generation
        console.log('Generate tests');
      },
      keywords: ['ai', 'generate', 'tests', 'unit', 'testing']
    },
    {
      id: 'ai.explain-code',
      title: 'Explain Code',
      description: 'Get AI explanation of selected code',
      category: 'AI',
      icon: <Zap size={16} />,
      action: () => {
        // TODO: Implement code explanation
        console.log('Explain code');
      },
      keywords: ['ai', 'explain', 'code', 'documentation', 'help']
    }
  ], [tabs, activeTabId, saveTab, closeTab, closeAllTabs, openFileDialog, createNewFile, saveFileDialog]);

  return { commands };
};

export default useCommands;
