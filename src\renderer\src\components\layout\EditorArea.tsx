import React, { useState, useEffect, useRef, useCallback } from 'react';
import Editor, { Monaco } from '@monaco-editor/react';
import * as monaco from 'monaco-editor';
import { useEditorStore } from '../../store/editorStore';
import { registerNusantaraGlowTheme, applyTheme } from '../../utils/monacoTheme';
import { getFileIcon } from '../../utils/languageDetection';
import { X, Save } from 'lucide-react';
import '../../styles/EditorArea.css';

const EditorArea: React.FC = () => {
  const {
    tabs,
    activeTabId,
    options,
    isEditorReady,
    monacoInstance,
    setActiveTab,
    closeTab,
    updateTabContent,
    saveTab,
    markTabAsDirty,
    updateTabViewState,
    setMonacoInstance,
    setEditorReady,
  } = useEditorStore();

  const editorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize Monaco Editor
  const handleEditorDidMount = useCallback((editor: monaco.editor.IStandaloneCodeEditor, monaco: Monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;
    setMonacoInstance(monaco);

    // Register custom theme
    registerNusantaraGlowTheme();
    applyTheme(options.theme);

    // Configure editor options
    editor.updateOptions({
      fontSize: options.fontSize,
      fontFamily: options.fontFamily,
      tabSize: options.tabSize,
      insertSpaces: options.insertSpaces,
      wordWrap: options.wordWrap,
      minimap: options.minimap,
      lineNumbers: options.lineNumbers,
      renderWhitespace: options.renderWhitespace,
      automaticLayout: true,
      scrollBeyondLastLine: false,
      glyphMargin: true,
      folding: true,
      lineDecorationsWidth: 10,
      lineNumbersMinChars: 3,
      smoothScrolling: true,
      cursorBlinking: 'smooth',
      cursorSmoothCaretAnimation: true,
      formatOnPaste: true,
      formatOnType: true,
      suggestOnTriggerCharacters: true,
      acceptSuggestionOnEnter: 'on',
      quickSuggestions: true,
      parameterHints: { enabled: true },
      hover: { enabled: true },
      contextmenu: true,
      mouseWheelZoom: true,
    });

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSaveActiveTab();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyW, () => {
      if (activeTabId) {
        handleTabClose(activeTabId);
      }
    });

    // Listen for content changes
    editor.onDidChangeModelContent(() => {
      if (activeTabId) {
        const content = editor.getValue();
        updateTabContent(activeTabId, content);
      }
    });

    // Listen for view state changes (cursor position, scroll position, etc.)
    editor.onDidChangeCursorPosition(() => {
      if (activeTabId) {
        const viewState = editor.saveViewState();
        updateTabViewState(activeTabId, viewState);
      }
    });

    editor.onDidScrollChange(() => {
      if (activeTabId) {
        const viewState = editor.saveViewState();
        updateTabViewState(activeTabId, viewState);
      }
    });

    setEditorReady(true);
    setIsLoading(false);
  }, [activeTabId, options, setMonacoInstance, setEditorReady, updateTabContent, updateTabViewState]);

  // Handle tab switching
  useEffect(() => {
    if (!editorRef.current || !activeTabId) return;

    const activeTab = tabs.find(tab => tab.id === activeTabId);
    if (!activeTab) return;

    const editor = editorRef.current;

    // Create or get model for this file
    const uri = monaco.Uri.file(activeTab.path);
    let model = monaco.editor.getModel(uri);

    if (!model) {
      model = monaco.editor.createModel(
        activeTab.content,
        activeTab.language,
        uri
      );
    } else {
      // Update model content if it's different
      if (model.getValue() !== activeTab.content) {
        model.setValue(activeTab.content);
      }
    }

    // Set the model to the editor
    editor.setModel(model);

    // Restore view state if available
    if (activeTab.viewState) {
      editor.restoreViewState(activeTab.viewState);
    }

    // Focus the editor
    editor.focus();
  }, [activeTabId, tabs]);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleTabClose = (tabId: string, e?: React.MouseEvent) => {
    e?.stopPropagation();

    const tab = tabs.find(t => t.id === tabId);
    if (tab?.isDirty) {
      // TODO: Show confirmation dialog for unsaved changes
      const shouldClose = window.confirm(`${tab.title} has unsaved changes. Close anyway?`);
      if (!shouldClose) return;
    }

    closeTab(tabId);
  };

  const handleSaveTab = async (tabId: string) => {
    const success = await saveTab(tabId);
    if (!success) {
      // TODO: Show error notification
      console.error('Failed to save file');
    }
  };

  const handleSaveActiveTab = () => {
    if (activeTabId) {
      handleSaveTab(activeTabId);
    }
  };

  const activeTab = tabs.find(tab => tab.id === activeTabId);
  
  // Handle editor content changes
  const handleEditorChange = (value: string | undefined) => {
    if (value === undefined || !activeTabId) return;
    updateTabContent(activeTabId, value);
  };

  return (
    <div className="editor-area">
      {tabs.length > 0 ? (
        <>
          <div className="editor-tabs">
            {tabs.map(tab => (
              <div
                key={tab.id}
                className={`editor-tab ${tab.id === activeTabId ? 'active' : ''} ${tab.isDirty ? 'dirty' : ''}`}
                onClick={() => handleTabClick(tab.id)}
              >
                <span className="tab-icon">{getFileIcon(tab.path)}</span>
                <span className="tab-title">{tab.title}</span>
                <div className="tab-actions">
                  {tab.isDirty && (
                    <button
                      className="tab-save"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSaveTab(tab.id);
                      }}
                      title="Save file"
                    >
                      <Save size={12} />
                    </button>
                  )}
                  <button
                    className="tab-close"
                    onClick={(e) => handleTabClose(tab.id, e)}
                    title="Close file"
                  >
                    <X size={12} />
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="editor-container">
            {isLoading && (
              <div className="editor-loading">
                <div className="loading-spinner">
                  <div className="spinner-ring"></div>
                </div>
              </div>
            )}

            {activeTab && (
              <Editor
                height="100%"
                language={activeTab.language}
                value={activeTab.content}
                onChange={handleEditorChange}
                theme={options.theme}
                options={{
                  fontSize: options.fontSize,
                  fontFamily: options.fontFamily,
                  tabSize: options.tabSize,
                  insertSpaces: options.insertSpaces,
                  wordWrap: options.wordWrap,
                  minimap: options.minimap,
                  lineNumbers: options.lineNumbers,
                  renderWhitespace: options.renderWhitespace,
                  automaticLayout: true,
                  scrollBeyondLastLine: false,
                  glyphMargin: true,
                  folding: true,
                  lineDecorationsWidth: 10,
                  lineNumbersMinChars: 3,
                  smoothScrolling: true,
                  cursorBlinking: 'smooth',
                  cursorSmoothCaretAnimation: 'on',
                  formatOnPaste: true,
                  formatOnType: true,
                }}
                onMount={handleEditorDidMount}
              />
            )}
          </div>
        </>
      ) : (
        <div className="empty-editor">
          <div className="empty-editor-content">
            <div className="empty-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14 2 14 8 20 8"/>
                <line x1="12" y1="18" x2="12" y2="12"/>
                <line x1="9" y1="15" x2="15" y2="15"/>
              </svg>
            </div>
            <h2>No files open</h2>
            <p>Open a file from the explorer to start editing</p>
            <div className="empty-actions">
              <button className="btn btn-primary">New File</button>
              <button className="btn">Open File</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EditorArea;
