/* KilatCode IDE - Nusantara Glow Theme */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: 'Segoe UI', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Roboto', sans-serif;
  background: #0a0a0a;
  color: #ffffff;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Nusantara Glow Color Palette */
:root {
  /* Primary Colors */
  --primary-bg: #0a0a0a;
  --secondary-bg: #1a1a2e;
  --tertiary-bg: #16213e;
  --accent-bg: #0f3460;
  
  /* Neon Accents */
  --neon-cyan: #00d4ff;
  --neon-blue: #0099cc;
  --neon-teal: #00ffff;
  --neon-purple: #8a2be2;
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b8b8b8;
  --text-muted: #888888;
  --text-accent: #00d4ff;
  
  /* Border Colors */
  --border-primary: #333333;
  --border-secondary: #444444;
  --border-accent: #00d4ff;
  
  /* Status Colors */
  --success: #00ff88;
  --warning: #ffaa00;
  --error: #ff4444;
  --info: #00d4ff;
  
  /* Shadows and Glows */
  --glow-cyan: 0 0 10px #00d4ff, 0 0 20px #00d4ff, 0 0 30px #00d4ff;
  --glow-blue: 0 0 10px #0099cc, 0 0 20px #0099cc, 0 0 30px #0099cc;
  --glow-teal: 0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 30px #00ffff;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

::-webkit-scrollbar-corner {
  background: var(--secondary-bg);
}

/* Selection Styling */
::selection {
  background: var(--neon-cyan);
  color: var(--primary-bg);
}

::-moz-selection {
  background: var(--neon-cyan);
  color: var(--primary-bg);
}

/* Focus Outline */
*:focus {
  outline: 2px solid var(--neon-cyan);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Batik Pattern Background */
.batik-pattern {
  position: relative;
}

.batik-pattern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
  background-image: 
    radial-gradient(circle at 25% 25%, var(--neon-cyan) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, var(--neon-teal) 1px, transparent 1px),
    radial-gradient(circle at 50% 50%, var(--neon-blue) 1.5px, transparent 1.5px);
  background-size: 50px 50px, 30px 30px, 40px 40px;
  background-position: 0 0, 15px 15px, 25px 25px;
  pointer-events: none;
}

/* Glow Effects */
.glow-cyan {
  box-shadow: var(--glow-cyan);
}

.glow-blue {
  box-shadow: var(--glow-blue);
}

.glow-teal {
  box-shadow: var(--glow-teal);
}

/* Neon Text */
.neon-text {
  color: var(--neon-cyan);
  text-shadow: 0 0 5px var(--neon-cyan), 0 0 10px var(--neon-cyan), 0 0 15px var(--neon-cyan);
}

.neon-text-blue {
  color: var(--neon-blue);
  text-shadow: 0 0 5px var(--neon-blue), 0 0 10px var(--neon-blue), 0 0 15px var(--neon-blue);
}

.neon-text-teal {
  color: var(--neon-teal);
  text-shadow: 0 0 5px var(--neon-teal), 0 0 10px var(--neon-teal), 0 0 15px var(--neon-teal);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  user-select: none;
}

.btn:hover {
  background: var(--tertiary-bg);
  border-color: var(--border-accent);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.btn:active {
  transform: translateY(1px);
}

.btn-primary {
  background: linear-gradient(135deg, var(--neon-cyan), var(--neon-blue));
  border-color: var(--neon-cyan);
  color: var(--primary-bg);
  font-weight: 600;
}

.btn-primary:hover {
  box-shadow: var(--glow-cyan);
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  border-color: transparent;
}

.btn-ghost:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: var(--border-accent);
}

/* Input Styles */
.input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: 14px;
  transition: all var(--transition-fast);
}

.input:focus {
  border-color: var(--border-accent);
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.input::placeholder {
  color: var(--text-muted);
}

/* Card Styles */
.card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
}

.card:hover {
  border-color: var(--border-accent);
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.1);
}

/* Utility Classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

.text-center {
  text-align: center;
}

.text-sm {
  font-size: 12px;
}

.text-lg {
  font-size: 18px;
}

.text-xl {
  font-size: 24px;
}

.font-bold {
  font-weight: 600;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

.slide-up {
  animation: slideUp var(--transition-normal) ease-out;
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
