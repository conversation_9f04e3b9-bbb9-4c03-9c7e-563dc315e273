import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Download, 
  Trash2, 
  Power, 
  PowerOff, 
  Search, 
  Filter, 
  RefreshCw,
  Upload,
  ExternalLink,
  Settings,
  Star,
  Users,
  Calendar,
  Tag
} from 'lucide-react';
import '../styles/ExtensionManager.css';

// Extension types
export enum ExtensionType {
  VSIX = 'vsix',
  KOIX = 'koix'
}

export enum ExtensionState {
  INSTALLED = 'installed',
  ENABLED = 'enabled',
  DISABLED = 'disabled',
  UNINSTALLED = 'uninstalled'
}

// Extension interface
export interface Extension {
  id: string;
  name: string;
  displayName: string;
  description: string;
  version: string;
  publisher: string;
  type: ExtensionType;
  state: ExtensionState;
  path: string;
  installDate: Date;
  lastUpdated: Date;
  metadata: {
    icon?: string;
    categories?: string[];
    keywords?: string[];
    repository?: {
      url?: string;
    };
    homepage?: string;
    license?: string;
    preview?: boolean;
  };
}

interface ExtensionManagerProps {
  isVisible: boolean;
  onClose: () => void;
}

const ExtensionManager: React.FC<ExtensionManagerProps> = ({ isVisible, onClose }) => {
  const [extensions, setExtensions] = useState<Extension[]>([]);
  const [filteredExtensions, setFilteredExtensions] = useState<Extension[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'enabled' | 'disabled' | 'vsix' | 'koix'>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedExtension, setSelectedExtension] = useState<Extension | null>(null);

  // Load extensions on component mount
  useEffect(() => {
    if (isVisible) {
      loadExtensions();
    }
  }, [isVisible]);

  // Filter extensions when search query or filter type changes
  useEffect(() => {
    filterExtensions();
  }, [extensions, searchQuery, filterType]);

  /**
   * Load extensions from main process
   */
  const loadExtensions = async () => {
    try {
      setIsLoading(true);
      const extensionList = await window.electron.extensions.getAll();
      setExtensions(extensionList);
    } catch (error) {
      console.error('Failed to load extensions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Filter extensions based on search query and filter type
   */
  const filterExtensions = () => {
    let filtered = extensions;

    // Filter by type
    if (filterType !== 'all') {
      if (filterType === 'enabled') {
        filtered = filtered.filter(ext => ext.state === ExtensionState.ENABLED);
      } else if (filterType === 'disabled') {
        filtered = filtered.filter(ext => ext.state === ExtensionState.DISABLED);
      } else if (filterType === 'vsix') {
        filtered = filtered.filter(ext => ext.type === ExtensionType.VSIX);
      } else if (filterType === 'koix') {
        filtered = filtered.filter(ext => ext.type === ExtensionType.KOIX);
      }
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(ext => 
        ext.displayName.toLowerCase().includes(query) ||
        ext.description.toLowerCase().includes(query) ||
        ext.publisher.toLowerCase().includes(query) ||
        ext.metadata.keywords?.some(keyword => keyword.toLowerCase().includes(query))
      );
    }

    setFilteredExtensions(filtered);
  };

  /**
   * Install extension from file
   */
  const handleInstallFromFile = async () => {
    try {
      const result = await window.electron.dialog.openFile({
        filters: [
          { name: 'Extension Files', extensions: ['vsix', 'koix'] },
          { name: 'VSCode Extensions', extensions: ['vsix'] },
          { name: 'KilatCode Extensions', extensions: ['koix'] }
        ]
      });

      if (!result.canceled && result.filePaths.length > 0) {
        setIsLoading(true);
        const extensionId = await window.electron.extensions.install(result.filePaths[0]);
        
        if (extensionId) {
          await loadExtensions();
          // TODO: Show success notification
        } else {
          // TODO: Show error notification
        }
      }
    } catch (error) {
      console.error('Failed to install extension:', error);
      // TODO: Show error notification
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Enable extension
   */
  const handleEnableExtension = async (extensionId: string) => {
    try {
      setIsLoading(true);
      const success = await window.electron.extensions.enable(extensionId);
      
      if (success) {
        await loadExtensions();
        // TODO: Show success notification
      } else {
        // TODO: Show error notification
      }
    } catch (error) {
      console.error('Failed to enable extension:', error);
      // TODO: Show error notification
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Disable extension
   */
  const handleDisableExtension = async (extensionId: string) => {
    try {
      setIsLoading(true);
      const success = await window.electron.extensions.disable(extensionId);
      
      if (success) {
        await loadExtensions();
        // TODO: Show success notification
      } else {
        // TODO: Show error notification
      }
    } catch (error) {
      console.error('Failed to disable extension:', error);
      // TODO: Show error notification
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Uninstall extension
   */
  const handleUninstallExtension = async (extensionId: string) => {
    try {
      const confirmed = window.confirm('Are you sure you want to uninstall this extension?');
      
      if (!confirmed) return;

      setIsLoading(true);
      const success = await window.electron.extensions.uninstall(extensionId);
      
      if (success) {
        await loadExtensions();
        setSelectedExtension(null);
        // TODO: Show success notification
      } else {
        // TODO: Show error notification
      }
    } catch (error) {
      console.error('Failed to uninstall extension:', error);
      // TODO: Show error notification
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Get extension icon
   */
  const getExtensionIcon = (extension: Extension) => {
    if (extension.metadata.icon) {
      return extension.metadata.icon;
    }
    
    return extension.type === ExtensionType.VSIX ? '🧩' : '⚡';
  };

  /**
   * Get extension state color
   */
  const getStateColor = (state: ExtensionState) => {
    switch (state) {
      case ExtensionState.ENABLED:
        return 'var(--success)';
      case ExtensionState.DISABLED:
        return 'var(--warning)';
      case ExtensionState.INSTALLED:
        return 'var(--info)';
      default:
        return 'var(--text-muted)';
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="extension-manager">
      <div className="extension-manager-header">
        <div className="extension-manager-title">
          <Package size={20} />
          <span>Extension Manager</span>
        </div>
        <div className="extension-manager-actions">
          <button
            className="extension-action-btn"
            onClick={handleInstallFromFile}
            title="Install from File"
            disabled={isLoading}
          >
            <Upload size={16} />
            Install
          </button>
          <button
            className="extension-action-btn"
            onClick={loadExtensions}
            title="Refresh"
            disabled={isLoading}
          >
            <RefreshCw size={16} className={isLoading ? 'spinning' : ''} />
          </button>
          <button
            className="extension-action-btn close-btn"
            onClick={onClose}
            title="Close"
          >
            ×
          </button>
        </div>
      </div>

      <div className="extension-manager-toolbar">
        <div className="extension-search">
          <Search size={16} />
          <input
            type="text"
            placeholder="Search extensions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="extension-filter">
          <Filter size={16} />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
          >
            <option value="all">All Extensions</option>
            <option value="enabled">Enabled</option>
            <option value="disabled">Disabled</option>
            <option value="vsix">VSCode (.vsix)</option>
            <option value="koix">KilatCode (.koix)</option>
          </select>
        </div>
      </div>

      <div className="extension-manager-content">
        <div className="extension-list">
          {isLoading && (
            <div className="extension-loading">
              <div className="loading-spinner">
                <div className="spinner-ring"></div>
              </div>
              <p>Loading extensions...</p>
            </div>
          )}

          {!isLoading && filteredExtensions.length === 0 && (
            <div className="extension-empty">
              <Package size={48} />
              <h3>No Extensions Found</h3>
              <p>
                {searchQuery || filterType !== 'all' 
                  ? 'No extensions match your search criteria.'
                  : 'No extensions are installed. Install extensions to enhance your IDE experience.'
                }
              </p>
              {!searchQuery && filterType === 'all' && (
                <button className="btn btn-primary" onClick={handleInstallFromFile}>
                  Install Extension
                </button>
              )}
            </div>
          )}

          {!isLoading && filteredExtensions.map(extension => (
            <div
              key={extension.id}
              className={`extension-item ${selectedExtension?.id === extension.id ? 'selected' : ''}`}
              onClick={() => setSelectedExtension(extension)}
            >
              <div className="extension-icon">
                <span className="extension-emoji">{getExtensionIcon(extension)}</span>
              </div>
              
              <div className="extension-info">
                <div className="extension-header">
                  <h3 className="extension-name">{extension.displayName}</h3>
                  <div className="extension-badges">
                    <span 
                      className="extension-state-badge"
                      style={{ backgroundColor: getStateColor(extension.state) }}
                    >
                      {extension.state}
                    </span>
                    <span className="extension-type-badge">
                      {extension.type.toUpperCase()}
                    </span>
                  </div>
                </div>
                
                <p className="extension-description">{extension.description}</p>
                
                <div className="extension-meta">
                  <span className="extension-publisher">
                    <Users size={12} />
                    {extension.publisher}
                  </span>
                  <span className="extension-version">
                    <Tag size={12} />
                    v{extension.version}
                  </span>
                  <span className="extension-install-date">
                    <Calendar size={12} />
                    {new Date(extension.installDate).toLocaleDateString()}
                  </span>
                </div>
              </div>

              <div className="extension-actions">
                {extension.state === ExtensionState.ENABLED ? (
                  <button
                    className="extension-btn disable-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDisableExtension(extension.id);
                    }}
                    title="Disable Extension"
                    disabled={isLoading}
                  >
                    <PowerOff size={16} />
                  </button>
                ) : (
                  <button
                    className="extension-btn enable-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEnableExtension(extension.id);
                    }}
                    title="Enable Extension"
                    disabled={isLoading}
                  >
                    <Power size={16} />
                  </button>
                )}
                
                <button
                  className="extension-btn uninstall-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleUninstallExtension(extension.id);
                  }}
                  title="Uninstall Extension"
                  disabled={isLoading}
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          ))}
        </div>

        {selectedExtension && (
          <div className="extension-details">
            <div className="extension-details-header">
              <div className="extension-details-icon">
                <span className="extension-emoji">{getExtensionIcon(selectedExtension)}</span>
              </div>
              <div className="extension-details-info">
                <h2>{selectedExtension.displayName}</h2>
                <p className="extension-details-publisher">by {selectedExtension.publisher}</p>
                <div className="extension-details-badges">
                  <span 
                    className="extension-state-badge"
                    style={{ backgroundColor: getStateColor(selectedExtension.state) }}
                  >
                    {selectedExtension.state}
                  </span>
                  <span className="extension-type-badge">
                    {selectedExtension.type.toUpperCase()}
                  </span>
                  {selectedExtension.metadata.preview && (
                    <span className="extension-preview-badge">PREVIEW</span>
                  )}
                </div>
              </div>
            </div>

            <div className="extension-details-content">
              <div className="extension-details-section">
                <h3>Description</h3>
                <p>{selectedExtension.description}</p>
              </div>

              <div className="extension-details-section">
                <h3>Information</h3>
                <div className="extension-details-grid">
                  <div className="extension-detail-item">
                    <label>Version:</label>
                    <span>v{selectedExtension.version}</span>
                  </div>
                  <div className="extension-detail-item">
                    <label>Publisher:</label>
                    <span>{selectedExtension.publisher}</span>
                  </div>
                  <div className="extension-detail-item">
                    <label>Type:</label>
                    <span>{selectedExtension.type.toUpperCase()}</span>
                  </div>
                  <div className="extension-detail-item">
                    <label>Install Date:</label>
                    <span>{new Date(selectedExtension.installDate).toLocaleDateString()}</span>
                  </div>
                  <div className="extension-detail-item">
                    <label>Last Updated:</label>
                    <span>{new Date(selectedExtension.lastUpdated).toLocaleDateString()}</span>
                  </div>
                  {selectedExtension.metadata.license && (
                    <div className="extension-detail-item">
                      <label>License:</label>
                      <span>{selectedExtension.metadata.license}</span>
                    </div>
                  )}
                </div>
              </div>

              {selectedExtension.metadata.categories && selectedExtension.metadata.categories.length > 0 && (
                <div className="extension-details-section">
                  <h3>Categories</h3>
                  <div className="extension-categories">
                    {selectedExtension.metadata.categories.map(category => (
                      <span key={category} className="extension-category-tag">
                        {category}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {selectedExtension.metadata.keywords && selectedExtension.metadata.keywords.length > 0 && (
                <div className="extension-details-section">
                  <h3>Keywords</h3>
                  <div className="extension-keywords">
                    {selectedExtension.metadata.keywords.map(keyword => (
                      <span key={keyword} className="extension-keyword-tag">
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              <div className="extension-details-actions">
                {selectedExtension.state === ExtensionState.ENABLED ? (
                  <button
                    className="btn btn-warning"
                    onClick={() => handleDisableExtension(selectedExtension.id)}
                    disabled={isLoading}
                  >
                    <PowerOff size={16} />
                    Disable Extension
                  </button>
                ) : (
                  <button
                    className="btn btn-success"
                    onClick={() => handleEnableExtension(selectedExtension.id)}
                    disabled={isLoading}
                  >
                    <Power size={16} />
                    Enable Extension
                  </button>
                )}
                
                <button
                  className="btn btn-danger"
                  onClick={() => handleUninstallExtension(selectedExtension.id)}
                  disabled={isLoading}
                >
                  <Trash2 size={16} />
                  Uninstall
                </button>

                {selectedExtension.metadata.repository?.url && (
                  <button
                    className="btn btn-secondary"
                    onClick={() => window.electron.shell.openExternal(selectedExtension.metadata.repository!.url!)}
                  >
                    <ExternalLink size={16} />
                    Repository
                  </button>
                )}

                {selectedExtension.metadata.homepage && (
                  <button
                    className="btn btn-secondary"
                    onClick={() => window.electron.shell.openExternal(selectedExtension.metadata.homepage!)}
                  >
                    <ExternalLink size={16} />
                    Homepage
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExtensionManager;
