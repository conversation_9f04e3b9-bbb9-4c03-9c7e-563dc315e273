/* Visual Editor Styles */

.visual-editor {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-primary);
}

.visual-editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  min-height: 60px;
}

.visual-editor-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.project-name {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: normal;
}

.visual-editor-toolbar {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 2px;
  background: var(--tertiary-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--text-primary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
}

.toolbar-btn:hover {
  background: var(--accent-bg);
  color: var(--neon-cyan);
}

.toolbar-btn.active {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.toolbar-btn.close-btn {
  background: var(--error);
  color: white;
  font-size: 18px;
  font-weight: bold;
  margin-left: var(--spacing-md);
}

.toolbar-btn.close-btn:hover {
  background: #ff4444;
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
}

.zoom-level {
  padding: 0 var(--spacing-sm);
  font-size: 12px;
  color: var(--text-secondary);
  min-width: 40px;
  text-align: center;
}

.visual-editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Sidebars */
.visual-editor-sidebar {
  width: 280px;
  background: var(--secondary-bg);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.visual-editor-sidebar.right {
  border-right: none;
  border-left: 1px solid var(--border-primary);
}

.sidebar-section {
  border-bottom: 1px solid var(--border-secondary);
}

.sidebar-section h3 {
  margin: 0;
  padding: var(--spacing-md);
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: var(--tertiary-bg);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--tertiary-bg);
  border-bottom: 1px solid var(--border-secondary);
}

.section-header h3 {
  margin: 0;
  padding: 0;
  background: none;
}

.section-toggle {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  padding: 2px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.section-toggle:hover {
  background: var(--error);
  color: white;
}

/* Tools */
.tool-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
}

.tool-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--tertiary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.tool-btn:hover {
  background: var(--accent-bg);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.tool-btn.active {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
}

/* Layers */
.layers-list {
  max-height: 300px;
  overflow-y: auto;
}

.layer-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
}

.layer-item:hover {
  background: var(--tertiary-bg);
  border-left-color: var(--border-secondary);
}

.layer-item.selected {
  background: var(--accent-bg);
  border-left-color: var(--neon-cyan);
  box-shadow: inset 0 0 0 1px rgba(0, 212, 255, 0.2);
}

.layer-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: var(--primary-bg);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  flex-shrink: 0;
}

.layer-name {
  font-size: 13px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.layers-empty {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-muted);
}

.layers-empty p {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 13px;
}

/* Canvas */
.visual-editor-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--primary-bg);
}

.canvas-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
}

.canvas-loading .loading-spinner {
  margin-bottom: var(--spacing-md);
}

.canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.canvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--tertiary-bg);
  border-bottom: 1px solid var(--border-secondary);
}

.canvas-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

.canvas-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.canvas-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: var(--secondary-bg);
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.canvas-btn:hover {
  background: var(--accent-bg);
  color: var(--text-primary);
}

.canvas {
  background: white;
  border: 1px solid var(--border-secondary);
  margin: var(--spacing-md);
  border-radius: var(--radius-md);
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.design-view,
.code-view {
  width: 100%;
  height: 100%;
  position: relative;
}

.canvas-content {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.canvas-element {
  border: 2px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.canvas-element:hover {
  border-color: rgba(0, 212, 255, 0.5);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.canvas-element.selected {
  border-color: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
}

.canvas-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  text-align: center;
  padding: var(--spacing-xl);
}

.canvas-empty h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.canvas-empty p {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 14px;
}

.code-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  text-align: center;
}

.code-placeholder svg {
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.code-placeholder h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.code-placeholder p {
  margin: 0;
  font-size: 14px;
}

/* Properties Panel */
.element-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.element-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: var(--tertiary-bg);
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.element-action:hover {
  background: var(--accent-bg);
  color: var(--text-primary);
}

.properties-content {
  padding: var(--spacing-md);
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.property-group {
  margin-bottom: var(--spacing-lg);
}

.property-group h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.property-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
  font-size: 12px;
}

.property-item label {
  color: var(--text-secondary);
  font-weight: 500;
}

.property-item span {
  color: var(--text-primary);
  font-family: monospace;
}

.property-group textarea {
  width: 100%;
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 12px;
  resize: vertical;
  min-height: 60px;
}

.property-group textarea:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.style-section {
  margin-bottom: var(--spacing-md);
}

.style-section h5 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

.style-grid {
  display: grid;
  gap: var(--spacing-sm);
}

.style-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.style-item label {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
}

.style-item input,
.style-item select {
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: 4px 6px;
  color: var(--text-primary);
  font-size: 11px;
  height: 28px;
}

.style-item input[type="color"] {
  width: 100%;
  height: 28px;
  padding: 2px;
  cursor: pointer;
}

.style-item input:focus,
.style-item select:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 5px rgba(0, 212, 255, 0.2);
}

/* Custom scrollbars */
.visual-editor-sidebar::-webkit-scrollbar,
.layers-list::-webkit-scrollbar,
.properties-content::-webkit-scrollbar {
  width: 6px;
}

.visual-editor-sidebar::-webkit-scrollbar-track,
.layers-list::-webkit-scrollbar-track,
.properties-content::-webkit-scrollbar-track {
  background: var(--primary-bg);
}

.visual-editor-sidebar::-webkit-scrollbar-thumb,
.layers-list::-webkit-scrollbar-thumb,
.properties-content::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

.visual-editor-sidebar::-webkit-scrollbar-thumb:hover,
.layers-list::-webkit-scrollbar-thumb:hover,
.properties-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Responsive design */
@media (max-width: 1200px) {
  .visual-editor-sidebar {
    width: 240px;
  }
  
  .tool-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .visual-editor-toolbar {
    gap: var(--spacing-sm);
  }
  
  .toolbar-group {
    gap: 1px;
  }
  
  .toolbar-btn {
    width: 28px;
    height: 28px;
  }
  
  .visual-editor-sidebar {
    width: 200px;
  }
  
  .canvas {
    margin: var(--spacing-sm);
  }
}
