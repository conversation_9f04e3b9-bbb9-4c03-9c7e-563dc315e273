import { useCallback } from 'react';
import { useEditorStore } from '../store/editorStore';
import { getLanguageFromExtension } from '../utils/languageDetection';

export interface FileOperationResult {
  success: boolean;
  error?: string;
}

export const useFileOperations = () => {
  const { addTab, getTabByPath } = useEditorStore();

  /**
   * Open a file in the editor
   */
  const openFile = useCallback(async (filePath: string): Promise<FileOperationResult> => {
    try {
      // Check if file is already open
      const existingTab = getTabByPath(filePath);
      if (existingTab) {
        return { success: true };
      }

      // Read file content
      const content = await window.electron.fileSystem.readFile(filePath);
      
      // Extract file name from path
      const fileName = window.electron.path.basename(filePath);
      
      // Detect language
      const language = getLanguageFromExtension(filePath);
      
      // Add tab to editor
      addTab({
        title: fileName,
        path: filePath,
        language,
        content,
      });

      return { success: true };
    } catch (error) {
      console.error('Failed to open file:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }, [addTab, getTabByPath]);

  /**
   * Create a new file
   */
  const createNewFile = useCallback(async (fileName?: string, content: string = ''): Promise<FileOperationResult> => {
    try {
      // Generate a default name if not provided
      const defaultName = fileName || `untitled-${Date.now()}.txt`;
      
      // For now, create an unsaved file (no path)
      const language = getLanguageFromExtension(defaultName);
      
      addTab({
        title: defaultName,
        path: '', // Empty path indicates unsaved file
        language,
        content,
      });

      return { success: true };
    } catch (error) {
      console.error('Failed to create new file:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }, [addTab]);

  /**
   * Open file dialog and open selected file
   */
  const openFileDialog = useCallback(async (): Promise<FileOperationResult> => {
    try {
      const result = await window.electron.dialog.openFile({
        filters: [
          { name: 'All Files', extensions: ['*'] },
          { name: 'Text Files', extensions: ['txt', 'md', 'json', 'xml', 'csv'] },
          { name: 'Web Files', extensions: ['html', 'css', 'js', 'ts', 'jsx', 'tsx', 'vue', 'svelte'] },
          { name: 'Programming Files', extensions: ['py', 'java', 'cpp', 'c', 'cs', 'rs', 'go', 'php', 'rb'] },
          { name: 'Config Files', extensions: ['yaml', 'yml', 'toml', 'ini', 'conf', 'cfg'] },
        ]
      });

      if (result.canceled || !result.filePaths.length) {
        return { success: false, error: 'No file selected' };
      }

      return await openFile(result.filePaths[0]);
    } catch (error) {
      console.error('Failed to open file dialog:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }, [openFile]);

  /**
   * Save file dialog for unsaved files
   */
  const saveFileDialog = useCallback(async (content: string, defaultName?: string): Promise<FileOperationResult & { filePath?: string }> => {
    try {
      const result = await window.electron.dialog.saveFile({
        defaultPath: defaultName,
        filters: [
          { name: 'All Files', extensions: ['*'] },
          { name: 'Text Files', extensions: ['txt'] },
          { name: 'JavaScript', extensions: ['js'] },
          { name: 'TypeScript', extensions: ['ts'] },
          { name: 'HTML', extensions: ['html'] },
          { name: 'CSS', extensions: ['css'] },
          { name: 'JSON', extensions: ['json'] },
          { name: 'Markdown', extensions: ['md'] },
        ]
      });

      if (result.canceled || !result.filePath) {
        return { success: false, error: 'Save canceled' };
      }

      // Save the file
      await window.electron.fileSystem.writeFile(result.filePath, content);

      return { success: true, filePath: result.filePath };
    } catch (error) {
      console.error('Failed to save file:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }, []);

  /**
   * Open multiple files
   */
  const openMultipleFiles = useCallback(async (filePaths: string[]): Promise<FileOperationResult[]> => {
    const results: FileOperationResult[] = [];
    
    for (const filePath of filePaths) {
      const result = await openFile(filePath);
      results.push(result);
    }
    
    return results;
  }, [openFile]);

  /**
   * Check if file exists
   */
  const fileExists = useCallback(async (filePath: string): Promise<boolean> => {
    try {
      return await window.electron.fileSystem.exists(filePath);
    } catch {
      return false;
    }
  }, []);

  /**
   * Get file info
   */
  const getFileInfo = useCallback(async (filePath: string) => {
    try {
      const stats = await window.electron.fileSystem.stat(filePath);
      return {
        success: true,
        info: {
          size: stats.size,
          modified: new Date(stats.mtime),
          created: new Date(stats.birthtime),
          isDirectory: stats.isDirectory(),
          isFile: stats.isFile(),
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, []);

  /**
   * Watch file for changes
   */
  const watchFile = useCallback((filePath: string, callback: (eventType: string, filename: string) => void) => {
    try {
      return window.electron.fileSystem.watch(filePath, callback);
    } catch (error) {
      console.error('Failed to watch file:', error);
      return () => {}; // Return empty cleanup function
    }
  }, []);

  return {
    openFile,
    createNewFile,
    openFileDialog,
    saveFileDialog,
    openMultipleFiles,
    fileExists,
    getFileInfo,
    watchFile,
  };
};
