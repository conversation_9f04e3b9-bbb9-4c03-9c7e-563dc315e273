<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/kilatcode-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>KilatCode IDE</title>
    <style>
      /* Prevent flash of unstyled content */
      body {
        margin: 0;
        padding: 0;
        background-color: #0a0a0a;
        color: #ffffff;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        overflow: hidden;
      }
      
      /* Loading screen */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      #loading-screen.hidden {
        opacity: 0;
        pointer-events: none;
      }
      
      .lightning-logo {
        width: 120px;
        height: 120px;
        margin-bottom: 30px;
        position: relative;
      }
      
      .lightning-bolt {
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, #00d4ff, #0099cc, #00ffff);
        clip-path: polygon(20% 0%, 60% 0%, 40% 50%, 80% 50%, 60% 100%, 20% 100%, 40% 50%, 0% 50%);
        animation: lightning-glow 2s ease-in-out infinite alternate;
      }
      
      @keyframes lightning-glow {
        0% {
          filter: drop-shadow(0 0 10px #00d4ff) drop-shadow(0 0 20px #00d4ff) drop-shadow(0 0 30px #00d4ff);
          transform: scale(1);
        }
        100% {
          filter: drop-shadow(0 0 20px #00ffff) drop-shadow(0 0 40px #00ffff) drop-shadow(0 0 60px #00ffff);
          transform: scale(1.05);
        }
      }
      
      .loading-text {
        color: #00d4ff;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 20px;
        text-shadow: 0 0 10px #00d4ff;
      }
      
      .loading-subtitle {
        color: #888;
        font-size: 14px;
        margin-bottom: 30px;
      }
      
      .loading-bar {
        width: 300px;
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;
      }
      
      .loading-progress {
        height: 100%;
        background: linear-gradient(90deg, #00d4ff, #00ffff);
        border-radius: 2px;
        animation: loading-progress 2s ease-in-out infinite;
        box-shadow: 0 0 10px #00d4ff;
      }
      
      @keyframes loading-progress {
        0% {
          width: 0%;
        }
        50% {
          width: 70%;
        }
        100% {
          width: 100%;
        }
      }
      
      /* Batik pattern overlay */
      .batik-pattern {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.05;
        background-image: 
          radial-gradient(circle at 25% 25%, #00d4ff 2px, transparent 2px),
          radial-gradient(circle at 75% 75%, #00ffff 1px, transparent 1px),
          radial-gradient(circle at 50% 50%, #0099cc 1.5px, transparent 1.5px);
        background-size: 50px 50px, 30px 30px, 40px 40px;
        background-position: 0 0, 15px 15px, 25px 25px;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen with Lightning Animation -->
    <div id="loading-screen">
      <div class="batik-pattern"></div>
      <div class="lightning-logo">
        <div class="lightning-bolt"></div>
      </div>
      <div class="loading-text">KilatCode IDE</div>
      <div class="loading-subtitle">Nusantara Glow • Modern Development Environment</div>
      <div class="loading-bar">
        <div class="loading-progress"></div>
      </div>
    </div>
    
    <!-- Main App Container -->
    <div id="root"></div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <script>
      // Hide loading screen after app loads
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.classList.add('hidden');
            setTimeout(() => {
              loadingScreen.remove();
            }, 500);
          }
        }, 2000); // Show loading for 2 seconds minimum
      });
    </script>
  </body>
</html>
