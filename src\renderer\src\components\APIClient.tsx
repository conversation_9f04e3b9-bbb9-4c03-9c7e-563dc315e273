import React, { useState, useEffect } from 'react';
import { 
  Globe, 
  Send, 
  Plus, 
  Folder, 
  Clock, 
  Settings, 
  Download, 
  Upload,
  Copy,
  Trash2,
  Play,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronRight,
  Database
} from 'lucide-react';
import '../styles/APIClient.css';

// HTTP Methods
type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

// Request interface
interface APIRequest {
  id: string;
  name: string;
  method: HTTPMethod;
  url: string;
  headers: Record<string, string>;
  body?: {
    type: 'none' | 'json' | 'form' | 'raw' | 'binary';
    content: string;
  };
  auth?: {
    type: 'none' | 'basic' | 'bearer' | 'api-key';
    username?: string;
    password?: string;
    token?: string;
    apiKey?: string;
    apiKeyHeader?: string;
  };
  params: Array<{ key: string; value: string; enabled: boolean }>;
  timeout: number;
}

// Response interface
interface APIResponse {
  id: string;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  body: string;
  size: number;
  time: number;
  error?: string;
}

// Collection interface
interface APICollection {
  id: string;
  name: string;
  description?: string;
  requests: APIRequest[];
}

// Environment interface
interface APIEnvironment {
  id: string;
  name: string;
  variables: Array<{ key: string; value: string; enabled: boolean }>;
  isActive: boolean;
}

const HTTP_METHODS: HTTPMethod[] = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'];

const METHOD_COLORS: Record<HTTPMethod, string> = {
  GET: '#28a745',
  POST: '#007bff',
  PUT: '#ffc107',
  PATCH: '#17a2b8',
  DELETE: '#dc3545',
  HEAD: '#6c757d',
  OPTIONS: '#6f42c1'
};

interface APIClientProps {
  isVisible: boolean;
  onClose: () => void;
}

const APIClient: React.FC<APIClientProps> = ({ isVisible, onClose }) => {
  const [collections, setCollections] = useState<APICollection[]>([]);
  const [environments, setEnvironments] = useState<APIEnvironment[]>([]);
  const [activeEnvironment, setActiveEnvironment] = useState<APIEnvironment | null>(null);
  const [selectedRequest, setSelectedRequest] = useState<APIRequest | null>(null);
  const [response, setResponse] = useState<APIResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'params' | 'headers' | 'body' | 'auth'>('params');
  const [responseTab, setResponseTab] = useState<'body' | 'headers' | 'cookies'>('body');
  const [showEnvironments, setShowEnvironments] = useState(false);

  // Load data on mount
  useEffect(() => {
    if (isVisible) {
      loadData();
    }
  }, [isVisible]);

  /**
   * Load collections and environments
   */
  const loadData = async () => {
    try {
      const [collectionsData, environmentsData, activeEnv] = await Promise.all([
        window.electron.api.getAllCollections(),
        window.electron.api.getAllEnvironments(),
        window.electron.api.getActiveEnvironment()
      ]);
      
      setCollections(collectionsData);
      setEnvironments(environmentsData);
      setActiveEnvironment(activeEnv);
    } catch (error) {
      console.error('Failed to load API client data:', error);
    }
  };

  /**
   * Create new collection
   */
  const createCollection = async () => {
    const name = prompt('Collection name:');
    if (!name) return;

    try {
      const collection = await window.electron.api.createCollection(name);
      setCollections(prev => [...prev, collection]);
    } catch (error) {
      console.error('Failed to create collection:', error);
    }
  };

  /**
   * Create new request
   */
  const createRequest = async (collectionId: string) => {
    const name = prompt('Request name:');
    if (!name) return;

    try {
      const request = await window.electron.api.createRequest(collectionId, name);
      setCollections(prev => 
        prev.map(col => 
          col.id === collectionId 
            ? { ...col, requests: [...col.requests, request] }
            : col
        )
      );
      setSelectedRequest(request);
    } catch (error) {
      console.error('Failed to create request:', error);
    }
  };

  /**
   * Send request
   */
  const sendRequest = async () => {
    if (!selectedRequest) return;

    try {
      setIsLoading(true);
      setResponse(null);
      
      const result = await window.electron.api.sendRequest(selectedRequest);
      setResponse(result);
    } catch (error) {
      console.error('Failed to send request:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Update request field
   */
  const updateRequest = (field: keyof APIRequest, value: any) => {
    if (!selectedRequest) return;

    const updated = { ...selectedRequest, [field]: value };
    setSelectedRequest(updated);
    
    // TODO: Auto-save request
  };

  /**
   * Add parameter
   */
  const addParam = () => {
    if (!selectedRequest) return;
    
    const newParams = [...selectedRequest.params, { key: '', value: '', enabled: true }];
    updateRequest('params', newParams);
  };

  /**
   * Update parameter
   */
  const updateParam = (index: number, field: 'key' | 'value' | 'enabled', value: any) => {
    if (!selectedRequest) return;
    
    const newParams = [...selectedRequest.params];
    newParams[index] = { ...newParams[index], [field]: value };
    updateRequest('params', newParams);
  };

  /**
   * Remove parameter
   */
  const removeParam = (index: number) => {
    if (!selectedRequest) return;
    
    const newParams = selectedRequest.params.filter((_, i) => i !== index);
    updateRequest('params', newParams);
  };

  /**
   * Add header
   */
  const addHeader = () => {
    if (!selectedRequest) return;
    
    const key = prompt('Header name:');
    if (!key) return;
    
    const value = prompt('Header value:');
    if (value === null) return;
    
    const newHeaders = { ...selectedRequest.headers, [key]: value };
    updateRequest('headers', newHeaders);
  };

  /**
   * Update header
   */
  const updateHeader = (key: string, value: string) => {
    if (!selectedRequest) return;
    
    const newHeaders = { ...selectedRequest.headers, [key]: value };
    updateRequest('headers', newHeaders);
  };

  /**
   * Remove header
   */
  const removeHeader = (key: string) => {
    if (!selectedRequest) return;
    
    const newHeaders = { ...selectedRequest.headers };
    delete newHeaders[key];
    updateRequest('headers', newHeaders);
  };

  /**
   * Format response body
   */
  const formatResponseBody = (body: string, contentType?: string): string => {
    try {
      if (contentType?.includes('application/json')) {
        return JSON.stringify(JSON.parse(body), null, 2);
      }
    } catch (error) {
      // Return as-is if not valid JSON
    }
    return body;
  };

  /**
   * Get status color
   */
  const getStatusColor = (status: number): string => {
    if (status >= 200 && status < 300) return '#28a745';
    if (status >= 300 && status < 400) return '#ffc107';
    if (status >= 400 && status < 500) return '#fd7e14';
    if (status >= 500) return '#dc3545';
    return '#6c757d';
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="api-client">
      {/* Header */}
      <div className="api-client-header">
        <div className="api-client-title">
          <Database size={20} />
          <span>API Client</span>
        </div>
        
        <div className="api-client-actions">
          <div className="environment-selector">
            <button
              className="environment-btn"
              onClick={() => setShowEnvironments(!showEnvironments)}
            >
              <Globe size={14} />
              <span>{activeEnvironment?.name || 'No Environment'}</span>
              <ChevronDown size={14} />
            </button>
            
            {showEnvironments && (
              <div className="environment-dropdown">
                {environments.map(env => (
                  <div
                    key={env.id}
                    className={`environment-item ${env.isActive ? 'active' : ''}`}
                    onClick={() => {
                      setActiveEnvironment(env);
                      setShowEnvironments(false);
                    }}
                  >
                    {env.name}
                  </div>
                ))}
                <div className="environment-divider" />
                <div className="environment-item" onClick={() => console.log('Manage environments')}>
                  <Settings size={12} />
                  Manage Environments
                </div>
              </div>
            )}
          </div>
          
          <button className="api-action-btn" title="Import">
            <Upload size={16} />
          </button>
          <button className="api-action-btn" title="Export">
            <Download size={16} />
          </button>
          <button className="api-action-btn close-btn" onClick={onClose} title="Close">
            ×
          </button>
        </div>
      </div>

      <div className="api-client-content">
        {/* Sidebar - Collections */}
        <div className="api-sidebar">
          <div className="sidebar-header">
            <h3>Collections</h3>
            <button className="sidebar-action" onClick={createCollection} title="New Collection">
              <Plus size={14} />
            </button>
          </div>
          
          <div className="collections-list">
            {collections.map(collection => (
              <div key={collection.id} className="collection-item">
                <div className="collection-header">
                  <div className="collection-info">
                    <Folder size={14} />
                    <span className="collection-name">{collection.name}</span>
                  </div>
                  <button
                    className="collection-action"
                    onClick={() => createRequest(collection.id)}
                    title="Add Request"
                  >
                    <Plus size={12} />
                  </button>
                </div>
                
                <div className="requests-list">
                  {collection.requests.map(request => (
                    <div
                      key={request.id}
                      className={`request-item ${selectedRequest?.id === request.id ? 'selected' : ''}`}
                      onClick={() => setSelectedRequest(request)}
                    >
                      <span
                        className="request-method"
                        style={{ color: METHOD_COLORS[request.method] }}
                      >
                        {request.method}
                      </span>
                      <span className="request-name">{request.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            
            {collections.length === 0 && (
              <div className="collections-empty">
                <Database size={32} />
                <h3>No Collections</h3>
                <p>Create a collection to organize your API requests</p>
                <button className="btn btn-primary" onClick={createCollection}>
                  <Plus size={16} />
                  Create Collection
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="api-main">
          {selectedRequest ? (
            <>
              {/* Request Builder */}
              <div className="request-builder">
                <div className="request-line">
                  <select
                    value={selectedRequest.method}
                    onChange={(e) => updateRequest('method', e.target.value as HTTPMethod)}
                    className="method-select"
                    style={{ color: METHOD_COLORS[selectedRequest.method] }}
                  >
                    {HTTP_METHODS.map(method => (
                      <option key={method} value={method}>{method}</option>
                    ))}
                  </select>
                  
                  <input
                    type="text"
                    value={selectedRequest.url}
                    onChange={(e) => updateRequest('url', e.target.value)}
                    placeholder="Enter request URL..."
                    className="url-input"
                  />
                  
                  <button
                    className="send-btn"
                    onClick={sendRequest}
                    disabled={isLoading || !selectedRequest.url}
                  >
                    {isLoading ? (
                      <div className="loading-spinner">
                        <div className="spinner-ring"></div>
                      </div>
                    ) : (
                      <Send size={16} />
                    )}
                    Send
                  </button>
                </div>

                {/* Request Tabs */}
                <div className="request-tabs">
                  <div className="tab-headers">
                    {(['params', 'headers', 'body', 'auth'] as const).map(tab => (
                      <button
                        key={tab}
                        className={`tab-header ${activeTab === tab ? 'active' : ''}`}
                        onClick={() => setActiveTab(tab)}
                      >
                        {tab.charAt(0).toUpperCase() + tab.slice(1)}
                        {tab === 'params' && selectedRequest.params.length > 0 && (
                          <span className="tab-count">{selectedRequest.params.length}</span>
                        )}
                        {tab === 'headers' && Object.keys(selectedRequest.headers).length > 0 && (
                          <span className="tab-count">{Object.keys(selectedRequest.headers).length}</span>
                        )}
                      </button>
                    ))}
                  </div>

                  <div className="tab-content">
                    {/* Parameters Tab */}
                    {activeTab === 'params' && (
                      <div className="params-tab">
                        <div className="params-header">
                          <h4>Query Parameters</h4>
                          <button className="add-btn" onClick={addParam}>
                            <Plus size={14} />
                            Add Parameter
                          </button>
                        </div>
                        
                        <div className="params-list">
                          {selectedRequest.params.map((param, index) => (
                            <div key={index} className="param-row">
                              <input
                                type="checkbox"
                                checked={param.enabled}
                                onChange={(e) => updateParam(index, 'enabled', e.target.checked)}
                              />
                              <input
                                type="text"
                                value={param.key}
                                onChange={(e) => updateParam(index, 'key', e.target.value)}
                                placeholder="Key"
                                className="param-key"
                              />
                              <input
                                type="text"
                                value={param.value}
                                onChange={(e) => updateParam(index, 'value', e.target.value)}
                                placeholder="Value"
                                className="param-value"
                              />
                              <button
                                className="remove-btn"
                                onClick={() => removeParam(index)}
                                title="Remove"
                              >
                                <Trash2 size={12} />
                              </button>
                            </div>
                          ))}
                          
                          {selectedRequest.params.length === 0 && (
                            <div className="params-empty">
                              <p>No parameters added yet</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Headers Tab */}
                    {activeTab === 'headers' && (
                      <div className="headers-tab">
                        <div className="headers-header">
                          <h4>Headers</h4>
                          <button className="add-btn" onClick={addHeader}>
                            <Plus size={14} />
                            Add Header
                          </button>
                        </div>
                        
                        <div className="headers-list">
                          {Object.entries(selectedRequest.headers).map(([key, value]) => (
                            <div key={key} className="header-row">
                              <input
                                type="text"
                                value={key}
                                onChange={(e) => {
                                  const newKey = e.target.value;
                                  if (newKey !== key) {
                                    removeHeader(key);
                                    updateHeader(newKey, value);
                                  }
                                }}
                                className="header-key"
                              />
                              <input
                                type="text"
                                value={value}
                                onChange={(e) => updateHeader(key, e.target.value)}
                                className="header-value"
                              />
                              <button
                                className="remove-btn"
                                onClick={() => removeHeader(key)}
                                title="Remove"
                              >
                                <Trash2 size={12} />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Body Tab */}
                    {activeTab === 'body' && (
                      <div className="body-tab">
                        <div className="body-header">
                          <h4>Request Body</h4>
                          <select
                            value={selectedRequest.body?.type || 'none'}
                            onChange={(e) => updateRequest('body', { 
                              type: e.target.value as any, 
                              content: selectedRequest.body?.content || '' 
                            })}
                            className="body-type-select"
                          >
                            <option value="none">None</option>
                            <option value="json">JSON</option>
                            <option value="form">Form Data</option>
                            <option value="raw">Raw</option>
                          </select>
                        </div>
                        
                        {selectedRequest.body?.type !== 'none' && (
                          <textarea
                            value={selectedRequest.body?.content || ''}
                            onChange={(e) => updateRequest('body', {
                              ...selectedRequest.body,
                              content: e.target.value
                            })}
                            placeholder={
                              selectedRequest.body?.type === 'json' 
                                ? '{\n  "key": "value"\n}'
                                : 'Enter request body...'
                            }
                            className="body-textarea"
                            rows={10}
                          />
                        )}
                      </div>
                    )}

                    {/* Auth Tab */}
                    {activeTab === 'auth' && (
                      <div className="auth-tab">
                        <div className="auth-header">
                          <h4>Authorization</h4>
                          <select
                            value={selectedRequest.auth?.type || 'none'}
                            onChange={(e) => updateRequest('auth', { 
                              type: e.target.value as any 
                            })}
                            className="auth-type-select"
                          >
                            <option value="none">No Auth</option>
                            <option value="basic">Basic Auth</option>
                            <option value="bearer">Bearer Token</option>
                            <option value="api-key">API Key</option>
                          </select>
                        </div>
                        
                        {selectedRequest.auth?.type === 'basic' && (
                          <div className="auth-fields">
                            <input
                              type="text"
                              value={selectedRequest.auth.username || ''}
                              onChange={(e) => updateRequest('auth', {
                                ...selectedRequest.auth,
                                username: e.target.value
                              })}
                              placeholder="Username"
                              className="auth-input"
                            />
                            <input
                              type="password"
                              value={selectedRequest.auth.password || ''}
                              onChange={(e) => updateRequest('auth', {
                                ...selectedRequest.auth,
                                password: e.target.value
                              })}
                              placeholder="Password"
                              className="auth-input"
                            />
                          </div>
                        )}
                        
                        {selectedRequest.auth?.type === 'bearer' && (
                          <div className="auth-fields">
                            <input
                              type="text"
                              value={selectedRequest.auth.token || ''}
                              onChange={(e) => updateRequest('auth', {
                                ...selectedRequest.auth,
                                token: e.target.value
                              })}
                              placeholder="Bearer Token"
                              className="auth-input"
                            />
                          </div>
                        )}
                        
                        {selectedRequest.auth?.type === 'api-key' && (
                          <div className="auth-fields">
                            <input
                              type="text"
                              value={selectedRequest.auth.apiKeyHeader || ''}
                              onChange={(e) => updateRequest('auth', {
                                ...selectedRequest.auth,
                                apiKeyHeader: e.target.value
                              })}
                              placeholder="Header Name (e.g., X-API-Key)"
                              className="auth-input"
                            />
                            <input
                              type="text"
                              value={selectedRequest.auth.apiKey || ''}
                              onChange={(e) => updateRequest('auth', {
                                ...selectedRequest.auth,
                                apiKey: e.target.value
                              })}
                              placeholder="API Key"
                              className="auth-input"
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Response Viewer */}
              {response && (
                <div className="response-viewer">
                  <div className="response-header">
                    <div className="response-status">
                      <span
                        className="status-code"
                        style={{ color: getStatusColor(response.status) }}
                      >
                        {response.status} {response.statusText}
                      </span>
                      <span className="response-time">{response.time}ms</span>
                      <span className="response-size">{response.size} bytes</span>
                    </div>
                    
                    <div className="response-actions">
                      <button className="response-action" title="Copy Response">
                        <Copy size={14} />
                      </button>
                      <button className="response-action" title="Save Response">
                        <Download size={14} />
                      </button>
                    </div>
                  </div>

                  <div className="response-tabs">
                    <div className="tab-headers">
                      {(['body', 'headers'] as const).map(tab => (
                        <button
                          key={tab}
                          className={`tab-header ${responseTab === tab ? 'active' : ''}`}
                          onClick={() => setResponseTab(tab)}
                        >
                          {tab.charAt(0).toUpperCase() + tab.slice(1)}
                        </button>
                      ))}
                    </div>

                    <div className="tab-content">
                      {responseTab === 'body' && (
                        <div className="response-body">
                          {response.error ? (
                            <div className="response-error">
                              <h4>Error</h4>
                              <p>{response.error}</p>
                            </div>
                          ) : (
                            <pre className="response-content">
                              {formatResponseBody(response.body, response.headers['content-type'])}
                            </pre>
                          )}
                        </div>
                      )}

                      {responseTab === 'headers' && (
                        <div className="response-headers">
                          {Object.entries(response.headers).map(([key, value]) => (
                            <div key={key} className="response-header-item">
                              <span className="header-name">{key}:</span>
                              <span className="header-value">{value}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="api-empty">
              <Database size={64} />
              <h2>API Client</h2>
              <p>Select a request from the sidebar or create a new collection to get started</p>
              <button className="btn btn-primary" onClick={createCollection}>
                <Plus size={16} />
                Create Collection
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default APIClient;
