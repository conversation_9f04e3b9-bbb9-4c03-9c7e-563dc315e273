# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
dist-ssr
build
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Electron specific
out/
app/

# SQLite databases
*.sqlite
*.sqlite3
*.db

# Prisma
prisma/*.db
prisma/migrations/

# Testing
coverage/

# Temporary files
.tmp/
.temp/

# Cache
.cache/
.parcel-cache/

# Plugins and extensions
plugins/*
!plugins/.gitkeep
extensions/*
!extensions/.gitkeep
