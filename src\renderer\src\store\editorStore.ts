import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';

export interface EditorTab {
  id: string;
  title: string;
  path: string;
  language: string;
  content: string;
  isDirty: boolean;
  lastModified: number;
  viewState?: any; // Monaco editor view state
}

export interface EditorOptions {
  fontSize: number;
  fontFamily: string;
  tabSize: number;
  insertSpaces: boolean;
  wordWrap: 'on' | 'off' | 'wordWrapColumn' | 'bounded';
  minimap: {
    enabled: boolean;
    side: 'right' | 'left';
    showSlider: 'always' | 'mouseover';
  };
  lineNumbers: 'on' | 'off' | 'relative';
  renderWhitespace: 'none' | 'boundary' | 'selection' | 'trailing' | 'all';
  theme: string;
  autoSave: boolean;
  autoSaveDelay: number;
}

interface EditorState {
  tabs: EditorTab[];
  activeTabId: string | null;
  recentFiles: string[];
  options: EditorOptions;
  isEditorReady: boolean;
  monacoInstance: any | null;
  
  // Actions
  addTab: (tab: Omit<EditorTab, 'id' | 'isDirty' | 'lastModified'>) => string;
  closeTab: (tabId: string) => void;
  closeAllTabs: () => void;
  setActiveTab: (tabId: string) => void;
  updateTabContent: (tabId: string, content: string) => void;
  saveTab: (tabId: string, content?: string) => Promise<boolean>;
  markTabAsDirty: (tabId: string, isDirty: boolean) => void;
  updateTabViewState: (tabId: string, viewState: any) => void;
  updateOptions: (options: Partial<EditorOptions>) => void;
  setMonacoInstance: (instance: any) => void;
  setEditorReady: (ready: boolean) => void;
  getTabByPath: (path: string) => EditorTab | undefined;
  addRecentFile: (path: string) => void;
}

const DEFAULT_EDITOR_OPTIONS: EditorOptions = {
  fontSize: 14,
  fontFamily: 'Fira Code, Consolas, monospace',
  tabSize: 2,
  insertSpaces: true,
  wordWrap: 'on',
  minimap: {
    enabled: true,
    side: 'right',
    showSlider: 'mouseover',
  },
  lineNumbers: 'on',
  renderWhitespace: 'selection',
  theme: 'nusantara-glow', // Custom theme
  autoSave: true,
  autoSaveDelay: 1000,
};

export const useEditorStore = create<EditorState>()(
  persist(
    (set, get) => ({
      tabs: [],
      activeTabId: null,
      recentFiles: [],
      options: DEFAULT_EDITOR_OPTIONS,
      isEditorReady: false,
      monacoInstance: null,

      addTab: (tab) => {
        const { tabs } = get();
        
        // Check if tab with this path already exists
        const existingTab = tabs.find(t => t.path === tab.path);
        if (existingTab) {
          set({ activeTabId: existingTab.id });
          return existingTab.id;
        }
        
        const newTab: EditorTab = {
          id: uuidv4(),
          ...tab,
          isDirty: false,
          lastModified: Date.now(),
        };
        
        set(state => ({
          tabs: [...state.tabs, newTab],
          activeTabId: newTab.id,
        }));
        
        // Add to recent files
        get().addRecentFile(tab.path);
        
        return newTab.id;
      },
      
      closeTab: (tabId) => {
        const { tabs, activeTabId } = get();
        const newTabs = tabs.filter(tab => tab.id !== tabId);
        
        // If we're closing the active tab, set a new active tab
        let newActiveTabId = activeTabId;
        if (activeTabId === tabId) {
          const closedTabIndex = tabs.findIndex(tab => tab.id === tabId);
          if (newTabs.length > 0) {
            // Try to activate the tab to the right, or if it was the rightmost tab, the one to the left
            newActiveTabId = newTabs[closedTabIndex < newTabs.length ? closedTabIndex : newTabs.length - 1].id;
          } else {
            newActiveTabId = null;
          }
        }
        
        set({
          tabs: newTabs,
          activeTabId: newActiveTabId,
        });
      },
      
      closeAllTabs: () => {
        set({
          tabs: [],
          activeTabId: null,
        });
      },
      
      setActiveTab: (tabId) => {
        set({ activeTabId: tabId });
      },
      
      updateTabContent: (tabId, content) => {
        set(state => ({
          tabs: state.tabs.map(tab => 
            tab.id === tabId 
              ? { ...tab, content, isDirty: true, lastModified: Date.now() } 
              : tab
          ),
        }));
      },
      
      saveTab: async (tabId, content) => {
        const { tabs } = get();
        const tab = tabs.find(t => t.id === tabId);
        
        if (!tab) return false;
        
        try {
          // Use the provided content or the current tab content
          const contentToSave = content !== undefined ? content : tab.content;
          
          // Save file using Electron API
          await window.electron.fileSystem.writeFile(tab.path, contentToSave);
          
          // Update tab state
          set(state => ({
            tabs: state.tabs.map(t => 
              t.id === tabId 
                ? { ...t, content: contentToSave, isDirty: false, lastModified: Date.now() } 
                : t
            ),
          }));
          
          return true;
        } catch (error) {
          console.error('Failed to save file:', error);
          return false;
        }
      },
      
      markTabAsDirty: (tabId, isDirty) => {
        set(state => ({
          tabs: state.tabs.map(tab => 
            tab.id === tabId ? { ...tab, isDirty } : tab
          ),
        }));
      },
      
      updateTabViewState: (tabId, viewState) => {
        set(state => ({
          tabs: state.tabs.map(tab => 
            tab.id === tabId ? { ...tab, viewState } : tab
          ),
        }));
      },
      
      updateOptions: (options) => {
        set(state => ({
          options: { ...state.options, ...options },
        }));
      },
      
      setMonacoInstance: (instance) => {
        set({ monacoInstance: instance });
      },
      
      setEditorReady: (ready) => {
        set({ isEditorReady: ready });
      },
      
      getTabByPath: (path) => {
        return get().tabs.find(tab => tab.path === path);
      },
      
      addRecentFile: (path) => {
        set(state => {
          const recentFiles = [path, ...state.recentFiles.filter(p => p !== path)].slice(0, 10);
          return { recentFiles };
        });
      },
    }),
    {
      name: 'kilatcode-editor-storage',
      partialize: (state) => ({
        recentFiles: state.recentFiles,
        options: state.options,
      }),
    }
  )
);
