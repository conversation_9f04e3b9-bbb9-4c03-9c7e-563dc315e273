/* AI Assistant Styles */

.ai-assistant {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-primary);
}

.ai-assistant-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  min-height: 60px;
}

.ai-assistant-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.ai-status {
  margin-left: var(--spacing-md);
}

.ai-status-online,
.ai-status-offline {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.ai-status-online {
  background: rgba(0, 255, 136, 0.2);
  color: var(--success);
  border: 1px solid var(--success);
}

.ai-status-offline {
  background: rgba(255, 170, 0, 0.2);
  color: var(--warning);
  border: 1px solid var(--warning);
}

.ai-assistant-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.ai-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--tertiary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.ai-action-btn:hover {
  background: var(--accent-bg);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.ai-action-btn.close-btn {
  background: var(--error);
  border-color: var(--error);
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.ai-action-btn.close-btn:hover {
  background: #ff4444;
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
}

.ai-assistant-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Conversations Sidebar */
.ai-conversations {
  width: 280px;
  background: var(--secondary-bg);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
}

.ai-conversations-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
}

.ai-conversations-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ai-conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.ai-conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-bottom: var(--spacing-xs);
  border: 1px solid transparent;
}

.ai-conversation-item:hover {
  background: var(--tertiary-bg);
  border-color: var(--border-secondary);
}

.ai-conversation-item.active {
  background: var(--accent-bg);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.1);
}

.ai-conversation-info {
  flex: 1;
  min-width: 0;
}

.ai-conversation-title {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.ai-conversation-date {
  font-size: 11px;
  color: var(--text-muted);
}

.ai-conversation-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  opacity: 0;
}

.ai-conversation-item:hover .ai-conversation-delete {
  opacity: 1;
}

.ai-conversation-delete:hover {
  background: var(--error);
  color: white;
}

.ai-conversations-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-muted);
  height: 100%;
}

.ai-conversations-empty svg {
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.ai-conversations-empty p {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 14px;
}

/* Chat Area */
.ai-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-chat-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-muted);
  height: 100%;
}

.ai-chat-empty svg {
  margin-bottom: var(--spacing-lg);
  color: var(--neon-cyan);
  opacity: 0.7;
}

.ai-chat-empty h2 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 24px;
  color: var(--text-primary);
}

.ai-chat-empty p {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 16px;
  line-height: 1.5;
}

.ai-chat-empty p:last-of-type {
  margin-bottom: var(--spacing-lg);
}

.ai-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.ai-message {
  display: flex;
  gap: var(--spacing-sm);
  max-width: 80%;
}

.ai-message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message.assistant {
  align-self: flex-start;
}

.ai-message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  background: var(--tertiary-bg);
  border: 1px solid var(--border-secondary);
  flex-shrink: 0;
}

.ai-message.user .ai-message-avatar {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  border-color: var(--neon-cyan);
}

.ai-message.assistant .ai-message-avatar {
  background: var(--accent-bg);
  border-color: var(--neon-blue);
}

.ai-message-content {
  flex: 1;
  min-width: 0;
}

.ai-message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.ai-message-role {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
}

.ai-message-time {
  font-size: 11px;
  color: var(--text-muted);
}

.ai-message-text {
  background: var(--secondary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  word-wrap: break-word;
}

.ai-message.user .ai-message-text {
  background: var(--accent-bg);
  border-color: var(--neon-cyan);
}

.ai-message-text pre {
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  margin: var(--spacing-xs) 0;
  overflow-x: auto;
  font-family: 'Fira Code', monospace;
  font-size: 13px;
}

.ai-message-text code {
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: 3px;
  padding: 2px 4px;
  font-family: 'Fira Code', monospace;
  font-size: 13px;
}

.ai-message-text strong {
  font-weight: 600;
  color: var(--neon-cyan);
}

.ai-message-text em {
  font-style: italic;
  color: var(--text-secondary);
}

.ai-message-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.ai-message:hover .ai-message-actions {
  opacity: 1;
}

.ai-message-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: var(--tertiary-bg);
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.ai-message-action:hover {
  background: var(--accent-bg);
  color: var(--text-primary);
}

.ai-message-loading {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
}

.ai-typing-indicator {
  display: flex;
  gap: 4px;
}

.ai-typing-indicator span {
  width: 6px;
  height: 6px;
  background: var(--neon-cyan);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.ai-typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.ai-typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Quick Actions */
.ai-quick-actions {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-secondary);
  background: var(--tertiary-bg);
}

.ai-quick-actions-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-sm);
  display: block;
}

.ai-quick-actions-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.ai-quick-action {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--secondary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 12px;
  font-weight: 500;
}

.ai-quick-action:hover:not(:disabled) {
  background: var(--accent-bg);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.ai-quick-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Input Area */
.ai-input-area {
  border-top: 1px solid var(--border-secondary);
  background: var(--secondary-bg);
}

.ai-input-container {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  align-items: flex-end;
}

.ai-input {
  flex: 1;
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  min-height: 40px;
  max-height: 120px;
  font-family: inherit;
}

.ai-input:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.ai-input::placeholder {
  color: var(--text-muted);
}

.ai-send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--neon-cyan);
  color: var(--primary-bg);
  cursor: pointer;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.ai-send-btn:hover:not(:disabled) {
  background: #00b8e6;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
}

.ai-send-btn:disabled {
  background: var(--tertiary-bg);
  color: var(--text-muted);
  cursor: not-allowed;
}

.ai-context-info {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 11px;
  color: var(--text-muted);
  border-top: 1px solid var(--border-secondary);
  background: var(--tertiary-bg);
}

.ai-context-info span {
  margin-right: var(--spacing-sm);
}

/* Settings Panel */
.ai-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.ai-settings-panel {
  width: 100%;
  max-width: 500px;
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.ai-settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--tertiary-bg);
  border-bottom: 1px solid var(--border-secondary);
}

.ai-settings-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.ai-settings-header button {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.ai-settings-header button:hover {
  background: var(--error);
  color: white;
}

.ai-settings-content {
  padding: var(--spacing-lg);
  color: var(--text-secondary);
}

.ai-settings-content p {
  margin: 0 0 var(--spacing-sm) 0;
  line-height: 1.5;
}

/* Custom scrollbars */
.ai-conversations-list::-webkit-scrollbar,
.ai-messages::-webkit-scrollbar {
  width: 6px;
}

.ai-conversations-list::-webkit-scrollbar-track,
.ai-messages::-webkit-scrollbar-track {
  background: var(--primary-bg);
}

.ai-conversations-list::-webkit-scrollbar-thumb,
.ai-messages::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

.ai-conversations-list::-webkit-scrollbar-thumb:hover,
.ai-messages::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Responsive design */
@media (max-width: 768px) {
  .ai-conversations {
    width: 240px;
  }

  .ai-quick-actions-buttons {
    gap: var(--spacing-xs);
  }

  .ai-quick-action {
    padding: 4px 8px;
    font-size: 11px;
  }

  .ai-quick-action span {
    display: none;
  }

  .ai-message {
    max-width: 90%;
  }

  .ai-input-container {
    padding: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .ai-assistant-content {
    flex-direction: column;
  }

  .ai-conversations {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-primary);
  }

  .ai-chat {
    height: calc(100% - 200px);
  }
}
