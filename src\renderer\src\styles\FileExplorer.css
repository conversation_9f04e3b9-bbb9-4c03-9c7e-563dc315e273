/* File Explorer Styles */

.file-explorer {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.explorer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-primary);
  background: var(--secondary-bg);
}

.explorer-header h3 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.explorer-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.explorer-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.explorer-action:hover {
  background: var(--tertiary-bg);
  color: var(--text-primary);
}

.explorer-content {
  flex: 1;
  overflow: auto;
}

.explorer-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 12px;
}

.explorer-error {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  color: var(--error);
  font-size: 12px;
}

.file-tree {
  padding: var(--spacing-sm) 0;
}

.file-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  min-height: 24px;
}

.file-item:hover {
  background: var(--tertiary-bg);
}

.file-item.directory {
  font-weight: 500;
}

.file-icon {
  display: flex;
  align-items: center;
  gap: 2px;
  width: auto;
  height: 16px;
  color: var(--text-muted);
  flex-shrink: 0;
}

.chevron-icon {
  color: var(--text-muted);
}

.folder-icon {
  color: var(--neon-blue);
}

.file-emoji {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.file-name {
  font-size: 12px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.file-size {
  font-size: 11px;
  color: var(--text-muted);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.file-item:hover .file-size {
  opacity: 1;
}

.file-item.directory .file-name {
  color: var(--text-primary);
}

/* File type specific icons and colors */
.file-item[data-extension=".tsx"] .file-icon,
.file-item[data-extension=".ts"] .file-icon {
  color: #3178c6;
}

.file-item[data-extension=".jsx"] .file-icon,
.file-item[data-extension=".js"] .file-icon {
  color: #f7df1e;
}

.file-item[data-extension=".css"] .file-icon {
  color: #1572b6;
}

.file-item[data-extension=".html"] .file-icon {
  color: #e34f26;
}

.file-item[data-extension=".json"] .file-icon {
  color: #ffd700;
}

.file-item[data-extension=".md"] .file-icon {
  color: var(--text-secondary);
}

.file-item[data-extension=".png"] .file-icon,
.file-item[data-extension=".jpg"] .file-icon,
.file-item[data-extension=".jpeg"] .file-icon,
.file-item[data-extension=".gif"] .file-icon,
.file-item[data-extension=".svg"] .file-icon {
  color: #ff6b6b;
}

/* Scrollbar for file tree */
.explorer-content::-webkit-scrollbar {
  width: 6px;
}

.explorer-content::-webkit-scrollbar-track {
  background: transparent;
}

.explorer-content::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

.explorer-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}
