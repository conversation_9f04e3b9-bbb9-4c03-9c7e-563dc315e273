import React, { useState, useEffect } from 'react';
import { 
  GitBranch, 
  GitCommit, 
  GitMerge, 
  GitPullRequest, 
  Plus, 
  Minus, 
  RefreshCw, 
  Upload, 
  Download, 
  Clock, 
  User, 
  FileText, 
  Check, 
  X, 
  Eye,
  ChevronDown,
  ChevronRight,
  Circle,
  Dot
} from 'lucide-react';
import '../styles/GitPanel.css';

// Git interfaces
interface GitFile {
  path: string;
  status: 'untracked' | 'modified' | 'added' | 'deleted' | 'renamed' | 'copied' | 'unmerged';
  staged: boolean;
  workingTree: boolean;
  oldPath?: string;
}

interface GitStatus {
  branch: string;
  ahead: number;
  behind: number;
  files: GitFile[];
  clean: boolean;
  hasStaged: boolean;
  hasUnstaged: boolean;
  hasUntracked: boolean;
}

interface GitCommitInfo {
  hash: string;
  shortHash: string;
  author: string;
  email: string;
  date: Date;
  message: string;
  parents: string[];
  refs: string[];
}

interface GitBranchInfo {
  name: string;
  current: boolean;
  remote: boolean;
  upstream?: string;
  ahead?: number;
  behind?: number;
}

const STATUS_COLORS = {
  untracked: '#ffc107',
  modified: '#007bff',
  added: '#28a745',
  deleted: '#dc3545',
  renamed: '#17a2b8',
  copied: '#6f42c1',
  unmerged: '#fd7e14'
};

const STATUS_ICONS = {
  untracked: '?',
  modified: 'M',
  added: 'A',
  deleted: 'D',
  renamed: 'R',
  copied: 'C',
  unmerged: 'U'
};

interface GitPanelProps {
  workingDir: string;
  isVisible: boolean;
}

const GitPanel: React.FC<GitPanelProps> = ({ workingDir, isVisible }) => {
  const [status, setStatus] = useState<GitStatus | null>(null);
  const [branches, setBranches] = useState<GitBranchInfo[]>([]);
  const [commits, setCommits] = useState<GitCommitInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'changes' | 'history' | 'branches'>('changes');
  const [commitMessage, setCommitMessage] = useState('');
  const [showBranches, setShowBranches] = useState(false);
  const [showUnstaged, setShowUnstaged] = useState(true);
  const [showStaged, setShowStaged] = useState(true);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());

  // Load git data when component mounts or working directory changes
  useEffect(() => {
    if (isVisible && workingDir) {
      loadGitData();
    }
  }, [isVisible, workingDir]);

  /**
   * Load all git data
   */
  const loadGitData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadStatus(),
        loadBranches(),
        loadCommits()
      ]);
    } catch (error) {
      console.error('Failed to load git data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Load git status
   */
  const loadStatus = async () => {
    try {
      const gitStatus = await window.electron.git.getStatus(workingDir);
      setStatus(gitStatus);
    } catch (error) {
      console.error('Failed to load git status:', error);
    }
  };

  /**
   * Load branches
   */
  const loadBranches = async () => {
    try {
      const gitBranches = await window.electron.git.getBranches(workingDir);
      setBranches(gitBranches);
    } catch (error) {
      console.error('Failed to load branches:', error);
    }
  };

  /**
   * Load commit history
   */
  const loadCommits = async () => {
    try {
      const gitCommits = await window.electron.git.getCommitHistory(workingDir, 50);
      setCommits(gitCommits);
    } catch (error) {
      console.error('Failed to load commits:', error);
    }
  };

  /**
   * Stage files
   */
  const stageFiles = async (files: string[]) => {
    try {
      const success = await window.electron.git.stageFiles(workingDir, files);
      if (success) {
        await loadStatus();
        setSelectedFiles(new Set());
      }
    } catch (error) {
      console.error('Failed to stage files:', error);
    }
  };

  /**
   * Unstage files
   */
  const unstageFiles = async (files: string[]) => {
    try {
      const success = await window.electron.git.unstageFiles(workingDir, files);
      if (success) {
        await loadStatus();
        setSelectedFiles(new Set());
      }
    } catch (error) {
      console.error('Failed to unstage files:', error);
    }
  };

  /**
   * Commit changes
   */
  const commitChanges = async () => {
    if (!commitMessage.trim()) return;

    try {
      const success = await window.electron.git.commit(workingDir, commitMessage);
      if (success) {
        setCommitMessage('');
        await loadGitData();
      }
    } catch (error) {
      console.error('Failed to commit:', error);
    }
  };

  /**
   * Create new branch
   */
  const createBranch = async () => {
    const branchName = prompt('Branch name:');
    if (!branchName) return;

    try {
      const success = await window.electron.git.createBranch(workingDir, branchName, true);
      if (success) {
        await loadGitData();
      }
    } catch (error) {
      console.error('Failed to create branch:', error);
    }
  };

  /**
   * Switch branch
   */
  const switchBranch = async (branchName: string) => {
    try {
      const success = await window.electron.git.switchBranch(workingDir, branchName);
      if (success) {
        await loadGitData();
      }
    } catch (error) {
      console.error('Failed to switch branch:', error);
    }
  };

  /**
   * Push changes
   */
  const pushChanges = async () => {
    try {
      const success = await window.electron.git.push(workingDir);
      if (success) {
        await loadStatus();
      }
    } catch (error) {
      console.error('Failed to push:', error);
    }
  };

  /**
   * Pull changes
   */
  const pullChanges = async () => {
    try {
      const success = await window.electron.git.pull(workingDir);
      if (success) {
        await loadGitData();
      }
    } catch (error) {
      console.error('Failed to pull:', error);
    }
  };

  /**
   * Toggle file selection
   */
  const toggleFileSelection = (filePath: string) => {
    const newSelection = new Set(selectedFiles);
    if (newSelection.has(filePath)) {
      newSelection.delete(filePath);
    } else {
      newSelection.add(filePath);
    }
    setSelectedFiles(newSelection);
  };

  /**
   * Select all files in section
   */
  const selectAllFiles = (staged: boolean) => {
    if (!status) return;
    
    const files = status.files.filter(f => f.staged === staged);
    const newSelection = new Set(selectedFiles);
    
    files.forEach(file => newSelection.add(file.path));
    setSelectedFiles(newSelection);
  };

  /**
   * Get file status icon
   */
  const getFileStatusIcon = (file: GitFile) => {
    return STATUS_ICONS[file.status];
  };

  /**
   * Get file status color
   */
  const getFileStatusColor = (file: GitFile) => {
    return STATUS_COLORS[file.status];
  };

  /**
   * Format commit date
   */
  const formatCommitDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60));
        return `${minutes}m ago`;
      }
      return `${hours}h ago`;
    } else if (days < 7) {
      return `${days}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="git-panel">
      {/* Header */}
      <div className="git-panel-header">
        <div className="git-panel-title">
          <GitBranch size={16} />
          <span>Git</span>
          {status && (
            <span className="git-branch-name">{status.branch}</span>
          )}
        </div>
        
        <div className="git-panel-actions">
          <button
            className="git-action-btn"
            onClick={loadGitData}
            disabled={isLoading}
            title="Refresh"
          >
            <RefreshCw size={14} className={isLoading ? 'spinning' : ''} />
          </button>
          
          {status && status.ahead > 0 && (
            <button
              className="git-action-btn push-btn"
              onClick={pushChanges}
              title={`Push ${status.ahead} commit${status.ahead > 1 ? 's' : ''}`}
            >
              <Upload size={14} />
              <span>{status.ahead}</span>
            </button>
          )}
          
          {status && status.behind > 0 && (
            <button
              className="git-action-btn pull-btn"
              onClick={pullChanges}
              title={`Pull ${status.behind} commit${status.behind > 1 ? 's' : ''}`}
            >
              <Download size={14} />
              <span>{status.behind}</span>
            </button>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="git-tabs">
        {(['changes', 'history', 'branches'] as const).map(tab => (
          <button
            key={tab}
            className={`git-tab ${activeTab === tab ? 'active' : ''}`}
            onClick={() => setActiveTab(tab)}
          >
            {tab === 'changes' && <FileText size={14} />}
            {tab === 'history' && <Clock size={14} />}
            {tab === 'branches' && <GitBranch size={14} />}
            <span>{tab.charAt(0).toUpperCase() + tab.slice(1)}</span>
            
            {tab === 'changes' && status && !status.clean && (
              <span className="tab-badge">{status.files.length}</span>
            )}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="git-content">
        {/* Changes Tab */}
        {activeTab === 'changes' && (
          <div className="git-changes">
            {status ? (
              <>
                {status.clean ? (
                  <div className="git-clean">
                    <Check size={32} />
                    <h3>Working tree clean</h3>
                    <p>No changes to commit</p>
                  </div>
                ) : (
                  <>
                    {/* Staged Changes */}
                    {status.hasStaged && (
                      <div className="git-section">
                        <div className="git-section-header">
                          <button
                            className="section-toggle"
                            onClick={() => setShowStaged(!showStaged)}
                          >
                            {showStaged ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                            <span>Staged Changes ({status.files.filter(f => f.staged).length})</span>
                          </button>
                          
                          <div className="section-actions">
                            <button
                              className="section-action"
                              onClick={() => selectAllFiles(true)}
                              title="Select All"
                            >
                              <Check size={12} />
                            </button>
                            <button
                              className="section-action"
                              onClick={() => {
                                const stagedFiles = status.files.filter(f => f.staged).map(f => f.path);
                                unstageFiles(stagedFiles);
                              }}
                              title="Unstage All"
                            >
                              <Minus size={12} />
                            </button>
                          </div>
                        </div>
                        
                        {showStaged && (
                          <div className="git-files">
                            {status.files.filter(f => f.staged).map(file => (
                              <div
                                key={file.path}
                                className={`git-file ${selectedFiles.has(file.path) ? 'selected' : ''}`}
                                onClick={() => toggleFileSelection(file.path)}
                              >
                                <div className="file-info">
                                  <span
                                    className="file-status"
                                    style={{ color: getFileStatusColor(file) }}
                                  >
                                    {getFileStatusIcon(file)}
                                  </span>
                                  <span className="file-path">{file.path}</span>
                                  {file.oldPath && (
                                    <span className="file-old-path">← {file.oldPath}</span>
                                  )}
                                </div>
                                
                                <div className="file-actions">
                                  <button
                                    className="file-action"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      unstageFiles([file.path]);
                                    }}
                                    title="Unstage"
                                  >
                                    <Minus size={12} />
                                  </button>
                                  <button
                                    className="file-action"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // TODO: Open diff view
                                    }}
                                    title="View Changes"
                                  >
                                    <Eye size={12} />
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Unstaged Changes */}
                    {status.hasUnstaged && (
                      <div className="git-section">
                        <div className="git-section-header">
                          <button
                            className="section-toggle"
                            onClick={() => setShowUnstaged(!showUnstaged)}
                          >
                            {showUnstaged ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                            <span>Changes ({status.files.filter(f => f.workingTree).length})</span>
                          </button>
                          
                          <div className="section-actions">
                            <button
                              className="section-action"
                              onClick={() => selectAllFiles(false)}
                              title="Select All"
                            >
                              <Check size={12} />
                            </button>
                            <button
                              className="section-action"
                              onClick={() => {
                                const unstagedFiles = status.files.filter(f => f.workingTree).map(f => f.path);
                                stageFiles(unstagedFiles);
                              }}
                              title="Stage All"
                            >
                              <Plus size={12} />
                            </button>
                          </div>
                        </div>
                        
                        {showUnstaged && (
                          <div className="git-files">
                            {status.files.filter(f => f.workingTree).map(file => (
                              <div
                                key={file.path}
                                className={`git-file ${selectedFiles.has(file.path) ? 'selected' : ''}`}
                                onClick={() => toggleFileSelection(file.path)}
                              >
                                <div className="file-info">
                                  <span
                                    className="file-status"
                                    style={{ color: getFileStatusColor(file) }}
                                  >
                                    {getFileStatusIcon(file)}
                                  </span>
                                  <span className="file-path">{file.path}</span>
                                  {file.oldPath && (
                                    <span className="file-old-path">← {file.oldPath}</span>
                                  )}
                                </div>
                                
                                <div className="file-actions">
                                  <button
                                    className="file-action"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      stageFiles([file.path]);
                                    }}
                                    title="Stage"
                                  >
                                    <Plus size={12} />
                                  </button>
                                  <button
                                    className="file-action"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // TODO: Open diff view
                                    }}
                                    title="View Changes"
                                  >
                                    <Eye size={12} />
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Commit Section */}
                    {status.hasStaged && (
                      <div className="git-commit-section">
                        <textarea
                          value={commitMessage}
                          onChange={(e) => setCommitMessage(e.target.value)}
                          placeholder="Commit message..."
                          className="commit-message-input"
                          rows={3}
                        />
                        
                        <div className="commit-actions">
                          <button
                            className="commit-btn"
                            onClick={commitChanges}
                            disabled={!commitMessage.trim()}
                          >
                            <GitCommit size={14} />
                            Commit
                          </button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </>
            ) : (
              <div className="git-not-repo">
                <GitBranch size={32} />
                <h3>Not a Git Repository</h3>
                <p>Initialize a git repository to start tracking changes</p>
                <button
                  className="btn btn-primary"
                  onClick={() => window.electron.git.initRepository(workingDir)}
                >
                  <GitBranch size={16} />
                  Initialize Repository
                </button>
              </div>
            )}
          </div>
        )}

        {/* History Tab */}
        {activeTab === 'history' && (
          <div className="git-history">
            {commits.length > 0 ? (
              <div className="commit-list">
                {commits.map(commit => (
                  <div key={commit.hash} className="commit-item">
                    <div className="commit-graph">
                      <Dot size={12} className="commit-dot" />
                    </div>
                    
                    <div className="commit-content">
                      <div className="commit-header">
                        <span className="commit-message">{commit.message}</span>
                        <span className="commit-hash">{commit.shortHash}</span>
                      </div>
                      
                      <div className="commit-meta">
                        <span className="commit-author">
                          <User size={12} />
                          {commit.author}
                        </span>
                        <span className="commit-date">
                          <Clock size={12} />
                          {formatCommitDate(commit.date)}
                        </span>
                      </div>
                      
                      {commit.refs.length > 0 && (
                        <div className="commit-refs">
                          {commit.refs.map(ref => (
                            <span key={ref} className="commit-ref">
                              {ref}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="git-empty">
                <Clock size={32} />
                <h3>No Commits</h3>
                <p>No commit history available</p>
              </div>
            )}
          </div>
        )}

        {/* Branches Tab */}
        {activeTab === 'branches' && (
          <div className="git-branches">
            <div className="branches-header">
              <h3>Branches</h3>
              <button
                className="branch-action-btn"
                onClick={createBranch}
                title="Create Branch"
              >
                <Plus size={14} />
              </button>
            </div>
            
            <div className="branch-list">
              {branches.map(branch => (
                <div
                  key={branch.name}
                  className={`branch-item ${branch.current ? 'current' : ''}`}
                  onClick={() => !branch.current && switchBranch(branch.name)}
                >
                  <div className="branch-info">
                    <GitBranch size={14} />
                    <span className="branch-name">{branch.name}</span>
                    {branch.current && (
                      <span className="current-indicator">
                        <Circle size={8} />
                      </span>
                    )}
                  </div>
                  
                  {branch.upstream && (
                    <div className="branch-tracking">
                      <span className="upstream-name">{branch.upstream}</span>
                      {branch.ahead && branch.ahead > 0 && (
                        <span className="ahead-count">↑{branch.ahead}</span>
                      )}
                      {branch.behind && branch.behind > 0 && (
                        <span className="behind-count">↓{branch.behind}</span>
                      )}
                    </div>
                  )}
                </div>
              ))}
              
              {branches.length === 0 && (
                <div className="git-empty">
                  <GitBranch size={32} />
                  <h3>No Branches</h3>
                  <p>No branches found</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GitPanel;
