/* IDE Layout Styles */

.ide-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: var(--primary-bg);
}

.ide-main {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.ide-layout-container {
  display: flex;
  height: 100%;
  width: 100%;
}

.sidebar-container {
  min-width: 200px;
  max-width: 500px;
  background: var(--secondary-bg);
  border-right: 1px solid var(--border-primary);
  overflow: hidden;
}

.editor-container-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-area-container {
  flex: 1;
  overflow: hidden;
}

.terminal-container {
  border-top: 1px solid var(--border-primary);
  background: var(--primary-bg);
  overflow: hidden;
}

/* Split Pane Styles */
.Resizer {
  background: var(--border-primary);
  z-index: 1;
  box-sizing: border-box;
  background-clip: padding-box;
}

.Resizer.vertical {
  width: 1px;
  margin: 0 -5px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  cursor: col-resize;
}

.Resizer.vertical:hover {
  border-left: 5px solid rgba(0, 212, 255, 0.2);
  border-right: 5px solid rgba(0, 212, 255, 0.2);
}

.Resizer.vertical.disabled {
  cursor: default;
}

.Resizer.horizontal {
  height: 1px;
  margin: -5px 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  cursor: row-resize;
}

.Resizer.horizontal:hover {
  border-top: 5px solid rgba(0, 212, 255, 0.2);
  border-bottom: 5px solid rgba(0, 212, 255, 0.2);
}

.Resizer.horizontal.disabled {
  cursor: default;
}

.Pane {
  display: flex;
  overflow: hidden;
}

/* Loading Screen */
.ide-loading {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--tertiary-bg) 100%);
  gap: var(--spacing-lg);
}

.loading-text {
  color: var(--text-secondary);
  font-size: 16px;
  margin-top: var(--spacing-md);
}
