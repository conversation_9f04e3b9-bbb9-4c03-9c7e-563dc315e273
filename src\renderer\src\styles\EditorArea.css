/* Editor Area Styles */

.editor-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--primary-bg);
  overflow: hidden;
}

.editor-tabs {
  display: flex;
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  overflow-x: auto;
  overflow-y: hidden;
  min-height: 36px;
}

.editor-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0 var(--spacing-md);
  min-width: 120px;
  max-width: 200px;
  background: var(--secondary-bg);
  border-right: 1px solid var(--border-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  position: relative;
}

.editor-tab:hover {
  background: var(--tertiary-bg);
}

.editor-tab.active {
  background: var(--primary-bg);
  border-bottom: 2px solid var(--neon-cyan);
}

.editor-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--neon-cyan);
  box-shadow: 0 0 5px var(--neon-cyan);
}

.editor-tab.dirty {
  position: relative;
}

.editor-tab.dirty .tab-title::after {
  content: '●';
  color: var(--warning);
  margin-left: 4px;
  font-size: 10px;
}

.tab-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.tab-title {
  font-size: 12px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  display: flex;
  align-items: center;
}

.tab-actions {
  display: flex;
  align-items: center;
  gap: 2px;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.editor-tab:hover .tab-actions {
  opacity: 1;
}

.editor-tab.dirty .tab-actions {
  opacity: 1;
}

.tab-save,
.tab-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: none;
  background: transparent;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: 2px;
  transition: all var(--transition-fast);
}

.tab-save {
  color: var(--warning);
}

.tab-save:hover {
  background: rgba(255, 170, 0, 0.2);
  color: var(--warning);
}

.tab-close:hover {
  background: var(--border-secondary);
  color: var(--text-primary);
}

.editor-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Editor Loading State */
.editor-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-bg);
  z-index: 10;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.spinner-ring {
  width: 30px;
  height: 30px;
  border: 3px solid transparent;
  border-top-color: var(--neon-cyan);
  border-radius: 50%;
  animation: spinner-animation 1s linear infinite;
}

@keyframes spinner-animation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Monaco Editor Customization */
.monaco-editor {
  background: var(--primary-bg) !important;
}

.monaco-editor .margin {
  background: var(--primary-bg) !important;
}

.monaco-editor .monaco-editor-background {
  background: var(--primary-bg) !important;
}

.monaco-editor .current-line {
  background: rgba(0, 212, 255, 0.05) !important;
}

.monaco-editor .selected-text {
  background: rgba(0, 212, 255, 0.2) !important;
}

.monaco-editor .line-numbers {
  color: var(--text-muted) !important;
}

.monaco-editor .cursor {
  color: var(--neon-cyan) !important;
}

/* Empty Editor State */
.empty-editor {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-bg);
}

.empty-editor-content {
  text-align: center;
  max-width: 400px;
  padding: var(--spacing-xl);
}

.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-lg);
  color: var(--text-muted);
  opacity: 0.5;
}

.empty-editor-content h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.empty-editor-content p {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* Tab scrolling */
.editor-tabs::-webkit-scrollbar {
  height: 0;
}

.editor-tabs {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .editor-tab {
    min-width: 100px;
    max-width: 150px;
    padding: 0 var(--spacing-sm);
  }
  
  .tab-title {
    font-size: 11px;
  }
}
