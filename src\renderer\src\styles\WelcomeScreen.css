/* Welcome Screen Styles */

.welcome-screen {
  height: 100vh;
  width: 100vw;
  overflow: auto;
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--tertiary-bg) 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-lg);
}

.welcome-container {
  width: 100%;
  max-width: 1000px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  background: rgba(10, 10, 10, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-primary);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  max-height: 90vh;
  overflow: auto;
}

.welcome-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.logo-icon {
  width: 50px;
  height: 50px;
  position: relative;
}

.lightning-bolt {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-blue), var(--neon-teal));
  clip-path: polygon(20% 0%, 60% 0%, 40% 50%, 80% 50%, 60% 100%, 20% 100%, 40% 50%, 0% 50%);
  filter: drop-shadow(0 0 10px var(--neon-cyan));
  animation: pulse 2s ease-in-out infinite alternate;
}

.logo-text {
  font-size: 36px;
  font-weight: 700;
  letter-spacing: 1px;
}

.welcome-subtitle {
  color: var(--text-secondary);
  font-size: 16px;
  margin-top: var(--spacing-sm);
}

.welcome-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.welcome-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.action-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.action-card:hover {
  border-color: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.2);
  transform: translateY(-2px);
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--tertiary-bg);
  color: var(--neon-cyan);
  transition: all var(--transition-normal);
}

.action-card:hover .action-icon {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  box-shadow: 0 0 10px var(--neon-cyan);
}

.action-text h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
}

.action-text p {
  font-size: 14px;
  color: var(--text-secondary);
}

.recent-projects {
  margin-top: var(--spacing-lg);
}

.recent-projects h2 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.recent-projects h2::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--border-primary);
  margin-left: var(--spacing-md);
}

.loading-projects {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  color: var(--text-secondary);
}

.spinner-ring.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.projects-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.project-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.project-item:hover {
  border-color: var(--neon-cyan);
  background: var(--tertiary-bg);
}

.project-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  color: var(--neon-cyan);
}

.project-info {
  flex: 1;
}

.project-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
  color: var(--text-primary);
}

.project-info p {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.project-time {
  font-size: 11px;
  color: var(--text-muted);
}

.no-projects {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  color: var(--text-secondary);
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
}

.welcome-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-primary);
}

.footer-links {
  display: flex;
  gap: var(--spacing-md);
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 14px;
  transition: color var(--transition-fast);
}

.footer-link:hover {
  color: var(--neon-cyan);
}

.version-info {
  font-size: 12px;
  color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-container {
    padding: var(--spacing-md);
  }
  
  .welcome-actions {
    grid-template-columns: 1fr;
  }
  
  .logo-text {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .welcome-screen {
    padding: var(--spacing-sm);
  }
  
  .welcome-container {
    padding: var(--spacing-sm);
  }
  
  .welcome-footer {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}
