/* Command Palette Styles */

.command-palette-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 10000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10vh;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.command-palette {
  width: 100%;
  max-width: 600px;
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(0, 212, 255, 0.2),
    0 0 20px rgba(0, 212, 255, 0.1);
  overflow: hidden;
  animation: slideIn 0.2s ease-out;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.command-palette-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-secondary);
  background: var(--tertiary-bg);
}

.command-palette-search {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  position: relative;
}

.command-palette-search svg:first-child {
  color: var(--neon-cyan);
  flex-shrink: 0;
}

.command-palette-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: 18px;
  color: var(--text-primary);
  padding: var(--spacing-sm) 0;
  font-weight: 500;
}

.command-palette-input::placeholder {
  color: var(--text-muted);
}

.command-palette-shortcut {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  font-size: 12px;
  color: var(--text-muted);
  flex-shrink: 0;
}

.command-palette-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.command-palette-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-muted);
  min-height: 200px;
}

.command-palette-empty svg {
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.command-palette-empty h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 18px;
  color: var(--text-primary);
}

.command-palette-empty p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.command-palette-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm) 0;
  max-height: 400px;
}

.command-palette-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
}

.command-palette-item:hover,
.command-palette-item.selected {
  background: var(--accent-bg);
  border-left-color: var(--neon-cyan);
}

.command-palette-item.selected {
  box-shadow: inset 0 0 0 1px rgba(0, 212, 255, 0.2);
}

.command-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--tertiary-bg);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  flex-shrink: 0;
  border: 1px solid var(--border-secondary);
}

.command-palette-item:hover .command-item-icon,
.command-palette-item.selected .command-item-icon {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.command-item-content {
  flex: 1;
  min-width: 0;
}

.command-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2px;
}

.command-item-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.command-item-shortcut {
  font-size: 12px;
  color: var(--text-muted);
  background: var(--primary-bg);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-secondary);
  font-family: monospace;
  flex-shrink: 0;
  margin-left: var(--spacing-sm);
}

.command-item-description {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.command-item-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  flex-shrink: 0;
}

.command-item-category {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.command-item-arrow {
  color: var(--text-muted);
  opacity: 0;
  transition: all var(--transition-fast);
}

.command-palette-item:hover .command-item-arrow,
.command-palette-item.selected .command-item-arrow {
  opacity: 1;
  color: var(--neon-cyan);
}

.command-palette-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--tertiary-bg);
  border-top: 1px solid var(--border-secondary);
  font-size: 12px;
}

.command-palette-tips {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.command-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-muted);
}

.command-tip kbd {
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: 3px;
  padding: 2px 4px;
  font-size: 10px;
  font-family: monospace;
  color: var(--text-secondary);
  min-width: 16px;
  text-align: center;
}

.command-palette-count {
  color: var(--text-muted);
  font-weight: 500;
}

/* Custom scrollbar for command list */
.command-palette-list::-webkit-scrollbar {
  width: 6px;
}

.command-palette-list::-webkit-scrollbar-track {
  background: var(--primary-bg);
}

.command-palette-list::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

.command-palette-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Responsive design */
@media (max-width: 768px) {
  .command-palette-overlay {
    padding: var(--spacing-md);
    padding-top: 5vh;
  }
  
  .command-palette {
    max-width: none;
    max-height: 80vh;
  }
  
  .command-palette-header {
    padding: var(--spacing-md);
  }
  
  .command-palette-input {
    font-size: 16px;
  }
  
  .command-palette-item {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .command-item-icon {
    width: 28px;
    height: 28px;
  }
  
  .command-palette-tips {
    gap: var(--spacing-sm);
  }
  
  .command-tip {
    display: none;
  }
  
  .command-tip:first-child {
    display: flex;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .command-palette {
    border-width: 2px;
  }
  
  .command-palette-item.selected {
    background: var(--neon-cyan);
    color: var(--primary-bg);
  }
  
  .command-palette-item.selected .command-item-title,
  .command-palette-item.selected .command-item-description {
    color: var(--primary-bg);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .command-palette-overlay,
  .command-palette,
  .command-palette-item,
  .command-item-icon,
  .command-item-arrow {
    animation: none;
    transition: none;
  }
}
