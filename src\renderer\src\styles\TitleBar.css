/* Title Bar Styles */

.title-bar {
  height: 30px;
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-sm);
  user-select: none;
  -webkit-app-region: drag;
  position: relative;
  z-index: 1000;
}

.title-bar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  -webkit-app-region: no-drag;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--neon-cyan);
  font-weight: 600;
  font-size: 14px;
}

.lightning-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: var(--neon-cyan);
}

.app-name {
  font-size: 12px;
  font-weight: 600;
}

.project-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.project-name {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.title-bar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  -webkit-app-region: no-drag;
}

.menu-bar {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.menu-item {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.menu-item:hover {
  background: var(--tertiary-bg);
  color: var(--text-primary);
}

.title-bar-right {
  display: flex;
  align-items: center;
  -webkit-app-region: no-drag;
}

.window-controls {
  display: flex;
  align-items: center;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.control-btn:hover {
  background: var(--tertiary-bg);
  color: var(--text-primary);
}

.control-btn.close:hover {
  background: var(--error);
  color: white;
}

/* macOS specific styles */
@media (platform: darwin) {
  .title-bar {
    padding-left: 70px; /* Space for traffic lights */
  }
  
  .window-controls {
    display: none;
  }
}
