import { EventEmitter } from 'events';
import * as https from 'https';
import * as http from 'http';
import { URL } from 'url';
import { DatabaseManager } from './DatabaseManager';

// HTTP Methods
export type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

// Request interface
export interface APIRequest {
  id: string;
  name: string;
  method: HTTPMethod;
  url: string;
  headers: Record<string, string>;
  body?: {
    type: 'none' | 'json' | 'form' | 'raw' | 'binary';
    content: string;
    formData?: Array<{ key: string; value: string; type: 'text' | 'file' }>;
  };
  auth?: {
    type: 'none' | 'basic' | 'bearer' | 'api-key';
    username?: string;
    password?: string;
    token?: string;
    apiKey?: string;
    apiKeyHeader?: string;
  };
  params: Array<{ key: string; value: string; enabled: boolean }>;
  timeout: number;
  followRedirects: boolean;
  validateSSL: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Response interface
export interface APIResponse {
  id: string;
  requestId: string;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  body: string;
  size: number;
  time: number;
  timestamp: Date;
  error?: string;
}

// Collection interface
export interface APICollection {
  id: string;
  name: string;
  description?: string;
  requests: APIRequest[];
  folders: APIFolder[];
  variables: Array<{ key: string; value: string; enabled: boolean }>;
  auth?: APIRequest['auth'];
  createdAt: Date;
  updatedAt: Date;
}

// Folder interface
export interface APIFolder {
  id: string;
  name: string;
  description?: string;
  requests: APIRequest[];
  folders: APIFolder[];
  parentId?: string;
}

// Environment interface
export interface APIEnvironment {
  id: string;
  name: string;
  variables: Array<{ key: string; value: string; enabled: boolean }>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// History entry
export interface APIHistory {
  id: string;
  request: APIRequest;
  response: APIResponse;
  timestamp: Date;
}

/**
 * API Client Service
 * Provides REST API testing capabilities similar to Postman/Insomnia
 */
export class APIClientService extends EventEmitter {
  private db: DatabaseManager;
  private collections: Map<string, APICollection> = new Map();
  private environments: Map<string, APIEnvironment> = new Map();
  private history: APIHistory[] = [];
  private activeEnvironment: APIEnvironment | null = null;
  private isInitialized = false;

  constructor(db: DatabaseManager) {
    super();
    this.db = db;
  }

  /**
   * Initialize API Client Service
   */
  public async initialize(): Promise<void> {
    try {
      // Load collections from database
      await this.loadCollections();
      
      // Load environments from database
      await this.loadEnvironments();
      
      // Load history from database
      await this.loadHistory();
      
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize API Client:', error instanceof Error ? error.message : String(error));
      this.emit('error', error);
    }
  }

  /**
   * Load collections from database
   */
  private async loadCollections(): Promise<void> {
    try {
      const collections = await this.db.getAll('api_collections');
      for (const collection of collections) {
        this.collections.set(collection.id, collection);
      }
    } catch (error) {
      console.warn('Failed to load API collections:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Load environments from database
   */
  private async loadEnvironments(): Promise<void> {
    try {
      const environments = await this.db.getAll('api_environments');
      for (const env of environments) {
        this.environments.set(env.id, env);
        if (env.isActive) {
          this.activeEnvironment = env;
        }
      }
    } catch (error) {
      console.warn('Failed to load API environments:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Load history from database
   */
  private async loadHistory(): Promise<void> {
    try {
      const history = await this.db.getAll('api_history');
      this.history = history.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    } catch (error) {
      console.warn('Failed to load API history:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Send HTTP request
   */
  public async sendRequest(request: APIRequest): Promise<APIResponse> {
    try {
      const startTime = Date.now();
      
      // Process URL with variables
      const processedUrl = this.processVariables(request.url);
      const url = new URL(processedUrl);
      
      // Add query parameters
      for (const param of request.params) {
        if (param.enabled && param.key && param.value) {
          url.searchParams.append(param.key, this.processVariables(param.value));
        }
      }

      // Prepare headers
      const headers = { ...request.headers };
      
      // Process headers with variables
      for (const [key, value] of Object.entries(headers)) {
        headers[key] = this.processVariables(value);
      }

      // Add authentication headers
      if (request.auth) {
        this.addAuthHeaders(headers, request.auth);
      }

      // Prepare body
      let body: string | undefined;
      if (request.body && request.method !== 'GET' && request.method !== 'HEAD') {
        body = this.prepareRequestBody(request.body, headers);
      }

      // Make HTTP request
      const response = await this.makeHttpRequest({
        method: request.method,
        url: url.toString(),
        headers,
        body,
        timeout: request.timeout,
        followRedirects: request.followRedirects,
        validateSSL: request.validateSSL
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      const apiResponse: APIResponse = {
        id: this.generateResponseId(),
        requestId: request.id,
        status: response.statusCode || 0,
        statusText: response.statusMessage || '',
        headers: response.headers || {},
        body: response.body || '',
        size: Buffer.byteLength(response.body || '', 'utf8'),
        time: responseTime,
        timestamp: new Date()
      };

      // Add to history
      await this.addToHistory(request, apiResponse);

      this.emit('request-sent', { request, response: apiResponse });
      return apiResponse;
    } catch (error) {
      const errorResponse: APIResponse = {
        id: this.generateResponseId(),
        requestId: request.id,
        status: 0,
        statusText: 'Error',
        headers: {},
        body: '',
        size: 0,
        time: 0,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : String(error)
      };

      this.emit('request-error', { request, error: errorResponse });
      return errorResponse;
    }
  }

  /**
   * Make HTTP request
   */
  private async makeHttpRequest(options: {
    method: string;
    url: string;
    headers: Record<string, string>;
    body?: string;
    timeout: number;
    followRedirects: boolean;
    validateSSL: boolean;
  }): Promise<{ statusCode?: number; statusMessage?: string; headers?: Record<string, string>; body?: string }> {
    return new Promise((resolve, reject) => {
      const url = new URL(options.url);
      const isHttps = url.protocol === 'https:';
      const httpModule = isHttps ? https : http;

      const requestOptions = {
        hostname: url.hostname,
        port: url.port || (isHttps ? 443 : 80),
        path: url.pathname + url.search,
        method: options.method,
        headers: options.headers,
        timeout: options.timeout,
        rejectUnauthorized: options.validateSSL
      };

      const req = httpModule.request(requestOptions, (res) => {
        let body = '';
        
        res.on('data', (chunk) => {
          body += chunk;
        });

        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            statusMessage: res.statusMessage,
            headers: res.headers as Record<string, string>,
            body
          });
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (options.body) {
        req.write(options.body);
      }

      req.end();
    });
  }

  /**
   * Process variables in string
   */
  private processVariables(text: string): string {
    let processed = text;

    // Process environment variables
    if (this.activeEnvironment) {
      for (const variable of this.activeEnvironment.variables) {
        if (variable.enabled) {
          const regex = new RegExp(`{{${variable.key}}}`, 'g');
          processed = processed.replace(regex, variable.value);
        }
      }
    }

    return processed;
  }

  /**
   * Add authentication headers
   */
  private addAuthHeaders(headers: Record<string, string>, auth: APIRequest['auth']): void {
    if (!auth || auth.type === 'none') return;

    switch (auth.type) {
      case 'basic':
        if (auth.username && auth.password) {
          const credentials = Buffer.from(`${auth.username}:${auth.password}`).toString('base64');
          headers['Authorization'] = `Basic ${credentials}`;
        }
        break;
      
      case 'bearer':
        if (auth.token) {
          headers['Authorization'] = `Bearer ${auth.token}`;
        }
        break;
      
      case 'api-key':
        if (auth.apiKey && auth.apiKeyHeader) {
          headers[auth.apiKeyHeader] = auth.apiKey;
        }
        break;
    }
  }

  /**
   * Prepare request body
   */
  private prepareRequestBody(body: APIRequest['body'], headers: Record<string, string>): string {
    if (!body) return '';

    switch (body.type) {
      case 'json':
        headers['Content-Type'] = 'application/json';
        return this.processVariables(body.content);
      
      case 'form':
        headers['Content-Type'] = 'application/x-www-form-urlencoded';
        if (body.formData) {
          const params = new URLSearchParams();
          for (const item of body.formData) {
            if (item.type === 'text') {
              params.append(item.key, this.processVariables(item.value));
            }
          }
          return params.toString();
        }
        return this.processVariables(body.content);
      
      case 'raw':
        return this.processVariables(body.content);
      
      default:
        return body.content;
    }
  }

  /**
   * Create new collection
   */
  public async createCollection(name: string, description?: string): Promise<APICollection> {
    const collection: APICollection = {
      id: this.generateCollectionId(),
      name,
      description,
      requests: [],
      folders: [],
      variables: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.collections.set(collection.id, collection);
    await this.db.set('api_collections', collection.id, collection);
    
    this.emit('collection-created', collection);
    return collection;
  }

  /**
   * Create new request
   */
  public async createRequest(
    collectionId: string,
    name: string,
    method: HTTPMethod = 'GET',
    url: string = ''
  ): Promise<APIRequest> {
    const request: APIRequest = {
      id: this.generateRequestId(),
      name,
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'KilatCode-API-Client/1.0'
      },
      params: [],
      timeout: 30000,
      followRedirects: true,
      validateSSL: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const collection = this.collections.get(collectionId);
    if (collection) {
      collection.requests.push(request);
      collection.updatedAt = new Date();
      await this.db.set('api_collections', collectionId, collection);
    }

    this.emit('request-created', request);
    return request;
  }

  /**
   * Create new environment
   */
  public async createEnvironment(name: string): Promise<APIEnvironment> {
    const environment: APIEnvironment = {
      id: this.generateEnvironmentId(),
      name,
      variables: [],
      isActive: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.environments.set(environment.id, environment);
    await this.db.set('api_environments', environment.id, environment);
    
    this.emit('environment-created', environment);
    return environment;
  }

  /**
   * Set active environment
   */
  public async setActiveEnvironment(environmentId: string): Promise<void> {
    // Deactivate current environment
    if (this.activeEnvironment) {
      this.activeEnvironment.isActive = false;
      await this.db.set('api_environments', this.activeEnvironment.id, this.activeEnvironment);
    }

    // Activate new environment
    const environment = this.environments.get(environmentId);
    if (environment) {
      environment.isActive = true;
      this.activeEnvironment = environment;
      await this.db.set('api_environments', environmentId, environment);
      
      this.emit('environment-activated', environment);
    }
  }

  /**
   * Add to history
   */
  private async addToHistory(request: APIRequest, response: APIResponse): Promise<void> {
    const historyEntry: APIHistory = {
      id: this.generateHistoryId(),
      request: { ...request },
      response,
      timestamp: new Date()
    };

    this.history.unshift(historyEntry);
    
    // Keep only last 100 entries
    if (this.history.length > 100) {
      this.history = this.history.slice(0, 100);
    }

    await this.db.set('api_history', historyEntry.id, historyEntry);
  }

  /**
   * Get all collections
   */
  public getAllCollections(): APICollection[] {
    return Array.from(this.collections.values());
  }

  /**
   * Get collection by ID
   */
  public getCollection(id: string): APICollection | undefined {
    return this.collections.get(id);
  }

  /**
   * Get all environments
   */
  public getAllEnvironments(): APIEnvironment[] {
    return Array.from(this.environments.values());
  }

  /**
   * Get active environment
   */
  public getActiveEnvironment(): APIEnvironment | null {
    return this.activeEnvironment;
  }

  /**
   * Get history
   */
  public getHistory(limit: number = 50): APIHistory[] {
    return this.history.slice(0, limit);
  }

  /**
   * Update request
   */
  public async updateRequest(request: APIRequest): Promise<void> {
    request.updatedAt = new Date();
    
    // Find and update in collection
    for (const collection of this.collections.values()) {
      const index = collection.requests.findIndex(r => r.id === request.id);
      if (index !== -1) {
        collection.requests[index] = request;
        collection.updatedAt = new Date();
        await this.db.set('api_collections', collection.id, collection);
        break;
      }
    }

    this.emit('request-updated', request);
  }

  /**
   * Delete request
   */
  public async deleteRequest(requestId: string): Promise<boolean> {
    for (const collection of this.collections.values()) {
      const index = collection.requests.findIndex(r => r.id === requestId);
      if (index !== -1) {
        collection.requests.splice(index, 1);
        collection.updatedAt = new Date();
        await this.db.set('api_collections', collection.id, collection);
        
        this.emit('request-deleted', requestId);
        return true;
      }
    }
    return false;
  }

  /**
   * Delete collection
   */
  public async deleteCollection(collectionId: string): Promise<boolean> {
    try {
      this.collections.delete(collectionId);
      await this.db.delete('api_collections', collectionId);
      
      this.emit('collection-deleted', collectionId);
      return true;
    } catch (error) {
      console.error('Failed to delete collection:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * ID generators
   */
  private generateCollectionId(): string {
    return `col_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateResponseId(): string {
    return `res_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEnvironmentId(): string {
    return `env_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateHistoryId(): string {
    return `hist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Dispose service
   */
  public dispose(): void {
    this.collections.clear();
    this.environments.clear();
    this.history = [];
    this.activeEnvironment = null;
    this.removeAllListeners();
  }
}
