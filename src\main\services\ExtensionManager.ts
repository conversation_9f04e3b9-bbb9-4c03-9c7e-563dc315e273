import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { app, BrowserWindow } from 'electron';
import { EventEmitter } from 'events';
import * as yauzl from 'yauzl';
import * as mkdirp from 'mkdirp';
import * as rimraf from 'rimraf';
import { promisify } from 'util';
import { DatabaseManager } from './DatabaseManager';
import { FileSystemService } from './FileSystemService';

// Promisify rimraf
const rimrafAsync = promisify(rimraf);

// Extension types
export enum ExtensionType {
  VSIX = 'vsix',
  KOIX = 'koix'
}

// Extension states
export enum ExtensionState {
  INSTALLED = 'installed',
  ENABLED = 'enabled',
  DISABLED = 'disabled',
  UNINSTALLED = 'uninstalled'
}

// Extension metadata
export interface ExtensionMetadata {
  id: string;
  name: string;
  displayName: string;
  description: string;
  version: string;
  publisher: string;
  type: ExtensionType;
  main?: string;
  browser?: string;
  engines?: {
    vscode?: string;
    kilatcode?: string;
  };
  contributes?: {
    commands?: Array<{
      command: string;
      title: string;
      category?: string;
      icon?: string;
    }>;
    languages?: any[];
    grammars?: any[];
    themes?: any[];
    snippets?: any[];
    menus?: any;
    keybindings?: any[];
    configuration?: any;
    views?: any;
    viewsContainers?: any;
  };
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  extensionDependencies?: string[];
  activationEvents?: string[];
  icon?: string;
  galleryBanner?: {
    color?: string;
    theme?: string;
  };
  repository?: {
    type?: string;
    url?: string;
  };
  bugs?: {
    url?: string;
  };
  homepage?: string;
  categories?: string[];
  keywords?: string[];
  preview?: boolean;
  license?: string;
}

// Extension database model
export interface Extension {
  id: string;
  name: string;
  displayName: string;
  description: string;
  version: string;
  publisher: string;
  type: ExtensionType;
  state: ExtensionState;
  path: string;
  installDate: Date;
  lastUpdated: Date;
  metadata: ExtensionMetadata;
}

// Extension instance
export interface ExtensionInstance {
  id: string;
  path: string;
  metadata: ExtensionMetadata;
  state: ExtensionState;
  exports: any;
  activate: () => Promise<any>;
  deactivate: () => Promise<void>;
}

/**
 * Extension Manager Service
 * Manages VSCode (.vsix) and KilatCode (.koix) extensions
 */
export class ExtensionManager extends EventEmitter {
  private extensions: Map<string, ExtensionInstance> = new Map();
  private extensionsPath: string;
  private vsixExtensionsPath: string;
  private koixExtensionsPath: string;
  private db: DatabaseManager;
  private fs: FileSystemService;
  private mainWindow: BrowserWindow | null = null;

  constructor(db: DatabaseManager, fs: FileSystemService) {
    super();
    this.db = db;
    this.fs = fs;

    // Set extensions paths
    this.extensionsPath = path.join(app.getPath('userData'), 'extensions');
    this.vsixExtensionsPath = path.join(this.extensionsPath, 'vsix');
    this.koixExtensionsPath = path.join(this.extensionsPath, 'koix');

    // Create extensions directories if they don't exist
    this.createExtensionDirectories();
  }

  /**
   * Set the main window reference
   */
  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  /**
   * Create extension directories
   */
  private async createExtensionDirectories(): Promise<void> {
    try {
      // Create main extensions directory
      if (!fs.existsSync(this.extensionsPath)) {
        await fs.promises.mkdir(this.extensionsPath, { recursive: true });
      }

      // Create VSIX extensions directory
      if (!fs.existsSync(this.vsixExtensionsPath)) {
        await fs.promises.mkdir(this.vsixExtensionsPath, { recursive: true });
      }

      // Create KOIX extensions directory
      if (!fs.existsSync(this.koixExtensionsPath)) {
        await fs.promises.mkdir(this.koixExtensionsPath, { recursive: true });
      }
    } catch (error) {
      console.error('Failed to create extension directories:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Initialize extension manager
   */
  public async initialize(): Promise<void> {
    try {
      // Load installed extensions from database
      await this.loadInstalledExtensions();

      // Scan extensions directories for new extensions
      await this.scanExtensionDirectories();

      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize extension manager:', error instanceof Error ? error.message : String(error));
      this.emit('error', error);
    }
  }

  /**
   * Load installed extensions from database
   */
  private async loadInstalledExtensions(): Promise<void> {
    try {
      const extensions = await this.db.getAll('extensions');
      
      for (const extension of extensions) {
        // Skip uninstalled extensions
        if (extension.state === ExtensionState.UNINSTALLED) {
          continue;
        }

        // Check if extension directory exists
        const extensionPath = extension.path;
        if (!fs.existsSync(extensionPath)) {
          console.warn(`Extension directory not found: ${extensionPath}`);
          continue;
        }

        // Load extension
        await this.loadExtension(extension.id, extensionPath, extension.metadata, extension.state);
      }

      this.emit('extensions-loaded', Array.from(this.extensions.values()));
    } catch (error) {
      console.error('Failed to load installed extensions:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Scan extensions directories for new extensions
   */
  private async scanExtensionDirectories(): Promise<void> {
    try {
      // Scan VSIX extensions directory
      await this.scanDirectory(this.vsixExtensionsPath, ExtensionType.VSIX);

      // Scan KOIX extensions directory
      await this.scanDirectory(this.koixExtensionsPath, ExtensionType.KOIX);
    } catch (error) {
      console.error('Failed to scan extension directories:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Scan directory for extensions
   */
  private async scanDirectory(directory: string, type: ExtensionType): Promise<void> {
    try {
      const entries = await fs.promises.readdir(directory, { withFileTypes: true });
      
      for (const entry of entries) {
        if (entry.isDirectory()) {
          const extensionPath = path.join(directory, entry.name);
          const packageJsonPath = path.join(extensionPath, 'package.json');
          
          // Check if package.json exists
          if (fs.existsSync(packageJsonPath)) {
            try {
              // Read package.json
              const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf-8'));
              
              // Create extension ID
              const extensionId = `${packageJson.publisher}.${packageJson.name}`;
              
              // Check if extension is already loaded
              if (!this.extensions.has(extensionId)) {
                // Check if extension exists in database
                const existingExtension = await this.db.get('extensions', extensionId);
                
                if (existingExtension) {
                  // Update extension metadata
                  await this.updateExtensionMetadata(extensionId, packageJson);
                } else {
                  // Register new extension
                  await this.registerExtension(extensionId, extensionPath, packageJson, type);
                }
              }
            } catch (error) {
              console.error(`Failed to process extension at ${extensionPath}:`, error instanceof Error ? error.message : String(error));
            }
          }
        }
      }
    } catch (error) {
      console.error(`Failed to scan directory ${directory}:`, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Register a new extension
   */
  private async registerExtension(
    id: string, 
    extensionPath: string, 
    packageJson: any, 
    type: ExtensionType
  ): Promise<void> {
    try {
      // Create extension metadata
      const metadata: ExtensionMetadata = {
        id,
        name: packageJson.name,
        displayName: packageJson.displayName || packageJson.name,
        description: packageJson.description || '',
        version: packageJson.version || '0.0.0',
        publisher: packageJson.publisher || 'unknown',
        type,
        main: packageJson.main,
        browser: packageJson.browser,
        engines: packageJson.engines,
        contributes: packageJson.contributes,
        dependencies: packageJson.dependencies,
        devDependencies: packageJson.devDependencies,
        extensionDependencies: packageJson.extensionDependencies,
        activationEvents: packageJson.activationEvents,
        icon: packageJson.icon,
        galleryBanner: packageJson.galleryBanner,
        repository: packageJson.repository,
        bugs: packageJson.bugs,
        homepage: packageJson.homepage,
        categories: packageJson.categories,
        keywords: packageJson.keywords,
        preview: packageJson.preview,
        license: packageJson.license
      };

      // Create extension record
      const extension: Extension = {
        id,
        name: metadata.name,
        displayName: metadata.displayName,
        description: metadata.description,
        version: metadata.version,
        publisher: metadata.publisher,
        type,
        state: ExtensionState.INSTALLED,
        path: extensionPath,
        installDate: new Date(),
        lastUpdated: new Date(),
        metadata
      };

      // Save extension to database
      await this.db.set('extensions', id, extension);

      // Load extension
      await this.loadExtension(id, extensionPath, metadata, ExtensionState.INSTALLED);

      this.emit('extension-registered', id);
    } catch (error) {
      console.error(`Failed to register extension ${id}:`, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Update extension metadata
   */
  private async updateExtensionMetadata(id: string, packageJson: any): Promise<void> {
    try {
      // Get existing extension
      const extension = await this.db.get('extensions', id);
      
      if (!extension) {
        throw new Error(`Extension ${id} not found in database`);
      }

      // Update metadata
      extension.metadata = {
        ...extension.metadata,
        version: packageJson.version || extension.metadata.version,
        displayName: packageJson.displayName || extension.metadata.displayName,
        description: packageJson.description || extension.metadata.description,
        main: packageJson.main || extension.metadata.main,
        browser: packageJson.browser || extension.metadata.browser,
        engines: packageJson.engines || extension.metadata.engines,
        contributes: packageJson.contributes || extension.metadata.contributes,
        dependencies: packageJson.dependencies || extension.metadata.dependencies,
        devDependencies: packageJson.devDependencies || extension.metadata.devDependencies,
        extensionDependencies: packageJson.extensionDependencies || extension.metadata.extensionDependencies,
        activationEvents: packageJson.activationEvents || extension.metadata.activationEvents,
        icon: packageJson.icon || extension.metadata.icon,
        galleryBanner: packageJson.galleryBanner || extension.metadata.galleryBanner,
        repository: packageJson.repository || extension.metadata.repository,
        bugs: packageJson.bugs || extension.metadata.bugs,
        homepage: packageJson.homepage || extension.metadata.homepage,
        categories: packageJson.categories || extension.metadata.categories,
        keywords: packageJson.keywords || extension.metadata.keywords,
        preview: packageJson.preview || extension.metadata.preview,
        license: packageJson.license || extension.metadata.license
      };

      // Update extension record
      extension.version = packageJson.version || extension.version;
      extension.displayName = packageJson.displayName || extension.displayName;
      extension.description = packageJson.description || extension.description;
      extension.lastUpdated = new Date();

      // Save updated extension to database
      await this.db.set('extensions', id, extension);

      // Reload extension
      await this.loadExtension(id, extension.path, extension.metadata, extension.state);

      this.emit('extension-updated', id);
    } catch (error) {
      console.error(`Failed to update extension metadata for ${id}:`, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Load an extension
   */
  private async loadExtension(
    id: string, 
    extensionPath: string, 
    metadata: ExtensionMetadata, 
    state: ExtensionState
  ): Promise<void> {
    try {
      // Create extension instance
      const extension: ExtensionInstance = {
        id,
        path: extensionPath,
        metadata,
        state,
        exports: {},
        activate: async () => {
          try {
            // Check if extension has a main entry point
            if (metadata.main) {
              const mainPath = path.join(extensionPath, metadata.main);
              
              if (fs.existsSync(mainPath)) {
                // Load extension module
                const extensionModule = require(mainPath);
                
                // Call activate function if it exists
                if (typeof extensionModule.activate === 'function') {
                  extension.exports = await extensionModule.activate();
                }
              }
            }
            
            // Update extension state
            extension.state = ExtensionState.ENABLED;
            await this.updateExtensionState(id, ExtensionState.ENABLED);
            
            this.emit('extension-activated', id);
            return extension.exports;
          } catch (error) {
            console.error(`Failed to activate extension ${id}:`, error instanceof Error ? error.message : String(error));
            throw error;
          }
        },
        deactivate: async () => {
          try {
            // Check if extension has a main entry point
            if (metadata.main) {
              const mainPath = path.join(extensionPath, metadata.main);
              
              if (fs.existsSync(mainPath)) {
                // Load extension module
                const extensionModule = require(mainPath);
                
                // Call deactivate function if it exists
                if (typeof extensionModule.deactivate === 'function') {
                  await extensionModule.deactivate();
                }
              }
            }
            
            // Update extension state
            extension.state = ExtensionState.DISABLED;
            await this.updateExtensionState(id, ExtensionState.DISABLED);
            
            this.emit('extension-deactivated', id);
          } catch (error) {
            console.error(`Failed to deactivate extension ${id}:`, error instanceof Error ? error.message : String(error));
            throw error;
          }
        }
      };

      // Add extension to map
      this.extensions.set(id, extension);

      // Auto-activate extension if it's enabled
      if (state === ExtensionState.ENABLED) {
        await extension.activate();
      }

      this.emit('extension-loaded', id);
    } catch (error) {
      console.error(`Failed to load extension ${id}:`, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Update extension state
   */
  private async updateExtensionState(id: string, state: ExtensionState): Promise<void> {
    try {
      // Get extension from database
      const extension = await this.db.get('extensions', id);
      
      if (!extension) {
        throw new Error(`Extension ${id} not found in database`);
      }

      // Update state
      extension.state = state;
      extension.lastUpdated = new Date();

      // Save updated extension to database
      await this.db.set('extensions', id, extension);
    } catch (error) {
      console.error(`Failed to update extension state for ${id}:`, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Install an extension from a .vsix or .koix file
   */
  public async installExtension(filePath: string): Promise<string | null> {
    try {
      // Check file extension
      const ext = path.extname(filePath).toLowerCase();
      
      if (ext !== '.vsix' && ext !== '.koix') {
        throw new Error(`Unsupported extension format: ${ext}`);
      }

      // Determine extension type
      const type = ext === '.vsix' ? ExtensionType.VSIX : ExtensionType.KOIX;
      
      // Extract extension
      const extensionId = await this.extractExtension(filePath, type);
      
      this.emit('extension-installed', extensionId);
      return extensionId;
    } catch (error) {
      console.error('Failed to install extension:', error instanceof Error ? error.message : String(error));
      this.emit('extension-install-failed', filePath, error);
      return null;
    }
  }

  /**
   * Extract an extension from a .vsix or .koix file
   */
  private async extractExtension(filePath: string, type: ExtensionType): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      // Open the .vsix/.koix file
      yauzl.open(filePath, { lazyEntries: true }, (err, zipfile) => {
        if (err || !zipfile) {
          return reject(new Error(`Failed to open extension file: ${err?.message || 'Unknown error'}`));
        }

        let extensionId: string | null = null;
        let extensionPath: string | null = null;
        let packageJson: any = null;

        zipfile.on('entry', (entry) => {
          const fileName = entry.fileName;

          // Look for extension.vsixmanifest or package.json
          if (fileName === 'extension.vsixmanifest' || fileName === 'package.json') {
            // Read the file content
            zipfile.openReadStream(entry, (err, readStream) => {
              if (err || !readStream) {
                zipfile.close();
                return reject(new Error(`Failed to read ${fileName}: ${err?.message || 'Unknown error'}`));
              }

              let content = '';
              readStream.on('data', (chunk) => {
                content += chunk.toString();
              });

              readStream.on('end', () => {
                try {
                  if (fileName === 'package.json') {
                    packageJson = JSON.parse(content);
                    
                    // Create extension ID
                    if (packageJson.publisher && packageJson.name) {
                      extensionId = `${packageJson.publisher}.${packageJson.name}`;
                      
                      // Create extension path
                      const targetDir = type === ExtensionType.VSIX ? this.vsixExtensionsPath : this.koixExtensionsPath;
                      extensionPath = path.join(targetDir, extensionId);
                    }
                  }
                  
                  zipfile.readEntry();
                } catch (error) {
                  zipfile.close();
                  reject(new Error(`Failed to parse ${fileName}: ${error instanceof Error ? error.message : String(error)}`));
                }
              });
            });
          } else if (extensionId && extensionPath) {
            // Extract files to extension directory
            if (entry.fileName.startsWith('extension/')) {
              const targetPath = path.join(extensionPath, entry.fileName.substring('extension/'.length));
              
              if (entry.fileName.endsWith('/')) {
                // Create directory
                mkdirp.sync(targetPath);
                zipfile.readEntry();
              } else {
                // Create parent directory if it doesn't exist
                mkdirp.sync(path.dirname(targetPath));
                
                // Extract file
                zipfile.openReadStream(entry, (err, readStream) => {
                  if (err || !readStream) {
                    zipfile.close();
                    return reject(new Error(`Failed to extract ${entry.fileName}: ${err?.message || 'Unknown error'}`));
                  }

                  const writeStream = fs.createWriteStream(targetPath);
                  readStream.pipe(writeStream);
                  
                  writeStream.on('finish', () => {
                    zipfile.readEntry();
                  });
                  
                  writeStream.on('error', (error) => {
                    zipfile.close();
                    reject(new Error(`Failed to write ${targetPath}: ${error.message}`));
                  });
                });
              }
            } else {
              zipfile.readEntry();
            }
          } else {
            zipfile.readEntry();
          }
        });

        zipfile.on('end', async () => {
          if (!extensionId || !extensionPath || !packageJson) {
            return reject(new Error('Invalid extension package: missing package.json or publisher/name'));
          }

          try {
            // Register the extension
            await this.registerExtension(extensionId, extensionPath, packageJson, type);
            resolve(extensionId);
          } catch (error) {
            reject(new Error(`Failed to register extension: ${error instanceof Error ? error.message : String(error)}`));
          }
        });

        zipfile.on('error', (error) => {
          reject(new Error(`Error extracting extension: ${error.message}`));
        });

        // Start reading entries
        zipfile.readEntry();
      });
    });
  }

  /**
   * Uninstall an extension
   */
  public async uninstallExtension(id: string): Promise<boolean> {
    try {
      // Get extension
      const extension = this.extensions.get(id);
      
      if (!extension) {
        throw new Error(`Extension ${id} not found`);
      }

      // Deactivate extension if it's enabled
      if (extension.state === ExtensionState.ENABLED) {
        await extension.deactivate();
      }

      // Remove extension from map
      this.extensions.delete(id);

      // Update extension state in database
      await this.updateExtensionState(id, ExtensionState.UNINSTALLED);

      // Remove extension directory
      await rimrafAsync(extension.path);

      this.emit('extension-uninstalled', id);
      return true;
    } catch (error) {
      console.error(`Failed to uninstall extension ${id}:`, error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Enable an extension
   */
  public async enableExtension(id: string): Promise<boolean> {
    try {
      // Get extension
      const extension = this.extensions.get(id);
      
      if (!extension) {
        throw new Error(`Extension ${id} not found`);
      }

      // Activate extension
      await extension.activate();
      
      this.emit('extension-enabled', id);
      return true;
    } catch (error) {
      console.error(`Failed to enable extension ${id}:`, error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Disable an extension
   */
  public async disableExtension(id: string): Promise<boolean> {
    try {
      // Get extension
      const extension = this.extensions.get(id);
      
      if (!extension) {
        throw new Error(`Extension ${id} not found`);
      }

      // Deactivate extension
      await extension.deactivate();
      
      this.emit('extension-disabled', id);
      return true;
    } catch (error) {
      console.error(`Failed to disable extension ${id}:`, error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Get all extensions
   */
  public getAllExtensions(): ExtensionInstance[] {
    return Array.from(this.extensions.values());
  }

  /**
   * Get extension by ID
   */
  public getExtension(id: string): ExtensionInstance | undefined {
    return this.extensions.get(id);
  }

  /**
   * Get enabled extensions
   */
  public getEnabledExtensions(): ExtensionInstance[] {
    return Array.from(this.extensions.values()).filter(ext => ext.state === ExtensionState.ENABLED);
  }

  /**
   * Clean up resources
   */
  public dispose(): void {
    // Deactivate all extensions
    for (const extension of this.extensions.values()) {
      if (extension.state === ExtensionState.ENABLED) {
        extension.deactivate().catch(console.error);
      }
    }

    // Clear extensions map
    this.extensions.clear();

    // Remove all listeners
    this.removeAllListeners();
  }
}
