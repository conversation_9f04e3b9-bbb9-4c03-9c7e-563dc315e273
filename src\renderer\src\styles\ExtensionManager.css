/* Extension Manager Styles */

.extension-manager {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-primary);
}

.extension-manager-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  min-height: 60px;
}

.extension-manager-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.extension-manager-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.extension-action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--tertiary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
}

.extension-action-btn:hover {
  background: var(--accent-bg);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.extension-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.extension-action-btn.close-btn {
  background: var(--error);
  border-color: var(--error);
  color: white;
  font-size: 18px;
  font-weight: bold;
  padding: var(--spacing-xs) var(--spacing-sm);
}

.extension-action-btn.close-btn:hover {
  background: #ff4444;
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
}

.extension-manager-toolbar {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--tertiary-bg);
  border-bottom: 1px solid var(--border-primary);
}

.extension-search {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  max-width: 400px;
  position: relative;
}

.extension-search svg {
  position: absolute;
  left: var(--spacing-sm);
  color: var(--text-muted);
  z-index: 1;
}

.extension-search input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 40px;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--primary-bg);
  color: var(--text-primary);
  font-size: 14px;
}

.extension-search input:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.extension-filter {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.extension-filter select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--primary-bg);
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
}

.extension-filter select:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.extension-manager-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.extension-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  min-width: 400px;
}

.extension-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.extension-loading .loading-spinner {
  margin-bottom: var(--spacing-md);
}

.extension-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-secondary);
}

.extension-empty svg {
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.extension-empty h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.extension-empty p {
  margin: 0 0 var(--spacing-lg) 0;
  max-width: 300px;
  line-height: 1.5;
}

.extension-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--secondary-bg);
  margin-bottom: var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.extension-item:hover {
  border-color: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.1);
}

.extension-item.selected {
  border-color: var(--neon-cyan);
  background: var(--accent-bg);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
}

.extension-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--tertiary-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

.extension-emoji {
  font-size: 24px;
}

.extension-info {
  flex: 1;
  min-width: 0;
}

.extension-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.extension-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.extension-badges {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
  margin-left: var(--spacing-sm);
}

.extension-state-badge,
.extension-type-badge,
.extension-preview-badge {
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  color: white;
}

.extension-type-badge {
  background: var(--info);
}

.extension-preview-badge {
  background: var(--warning);
}

.extension-description {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.extension-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 12px;
  color: var(--text-muted);
}

.extension-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.extension-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.extension-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  background: var(--tertiary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.extension-btn:hover {
  background: var(--accent-bg);
}

.extension-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.extension-btn.enable-btn:hover {
  background: var(--success);
  border-color: var(--success);
  color: white;
}

.extension-btn.disable-btn:hover {
  background: var(--warning);
  border-color: var(--warning);
  color: white;
}

.extension-btn.uninstall-btn:hover {
  background: var(--error);
  border-color: var(--error);
  color: white;
}

.extension-details {
  width: 400px;
  border-left: 1px solid var(--border-primary);
  background: var(--secondary-bg);
  overflow-y: auto;
  padding: var(--spacing-md);
}

.extension-details-header {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
}

.extension-details-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--tertiary-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

.extension-details-icon .extension-emoji {
  font-size: 32px;
}

.extension-details-info h2 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 20px;
  color: var(--text-primary);
}

.extension-details-publisher {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 14px;
  color: var(--text-secondary);
}

.extension-details-badges {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.extension-details-section {
  margin-bottom: var(--spacing-lg);
}

.extension-details-section h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 16px;
  color: var(--text-primary);
}

.extension-details-section p {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.extension-details-grid {
  display: grid;
  gap: var(--spacing-sm);
}

.extension-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.extension-detail-item label {
  color: var(--text-secondary);
  font-weight: 500;
}

.extension-detail-item span {
  color: var(--text-primary);
}

.extension-categories,
.extension-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.extension-category-tag,
.extension-keyword-tag {
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.extension-category-tag {
  background: var(--info);
  color: white;
}

.extension-keyword-tag {
  background: var(--tertiary-bg);
  color: var(--text-secondary);
  border: 1px solid var(--border-secondary);
}

.extension-details-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-secondary);
}

.extension-details-actions .btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.extension-details-actions .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Spinning animation for refresh button */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Responsive design */
@media (max-width: 768px) {
  .extension-manager-content {
    flex-direction: column;
  }
  
  .extension-details {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--border-primary);
  }
  
  .extension-list {
    min-width: auto;
  }
  
  .extension-manager-toolbar {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .extension-search {
    max-width: none;
  }
}
