import React, { useState, useEffect } from 'react';
import SplitPane from 'react-split-pane';
import Sidebar from './Sidebar';
import EditorArea from './EditorArea';
import StatusBar from './StatusBar';
import TitleBar from './TitleBar';
import TerminalPanel from '../TerminalPanel';
import '../../styles/IDELayout.css';

interface IDELayoutProps {
  projectPath: string;
  onCloseProject: () => void;
}

const IDELayout: React.FC<IDELayoutProps> = ({ projectPath, onCloseProject }) => {
  const [sidebarWidth, setSidebarWidth] = useState<number>(250);
  const [projectName, setProjectName] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isTerminalVisible, setIsTerminalVisible] = useState<boolean>(false);
  const [terminalHeight, setTerminalHeight] = useState<number>(300);
  
  useEffect(() => {
    // Extract project name from path
    const pathParts = projectPath.split(/[/\\]/);
    const name = pathParts[pathParts.length - 1];
    setProjectName(name);
    
    // Simulate loading project
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [projectPath]);
  
  if (isLoading) {
    return (
      <div className="ide-loading">
        <div className="loading-spinner">
          <div className="spinner-ring"></div>
        </div>
        <div className="loading-text">Loading project...</div>
      </div>
    );
  }
  
  return (
    <div className="ide-layout">
      <TitleBar projectName={projectName} />

      <div className="ide-main">
        <div className="ide-layout-container">
          <div className="sidebar-container" style={{ width: `${sidebarWidth}px` }}>
            <Sidebar projectPath={projectPath} />
          </div>
          <div className="editor-container-main">
            <div className="editor-area-container" style={{
              height: isTerminalVisible ? `calc(100% - ${terminalHeight}px)` : '100%'
            }}>
              <EditorArea />
            </div>
            {isTerminalVisible && (
              <div className="terminal-container" style={{ height: `${terminalHeight}px` }}>
                <TerminalPanel
                  isVisible={isTerminalVisible}
                  onToggle={() => setIsTerminalVisible(!isTerminalVisible)}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      <StatusBar
        projectPath={projectPath}
        onToggleTerminal={() => setIsTerminalVisible(!isTerminalVisible)}
        isTerminalVisible={isTerminalVisible}
      />
    </div>
  );
};

export default IDELayout;
