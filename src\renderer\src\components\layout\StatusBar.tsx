import React, { useState, useEffect } from 'react';
import '../../styles/StatusBar.css';

interface StatusBarProps {
  projectPath: string;
  onToggleTerminal?: () => void;
  isTerminalVisible?: boolean;
}

const StatusBar: React.FC<StatusBarProps> = ({ projectPath, onToggleTerminal, isTerminalVisible }) => {
  const [gitBranch, setGitBranch] = useState<string>('main');
  const [gitStatus, setGitStatus] = useState<string>('clean');
  const [lineCol, setLineCol] = useState<{ line: number; col: number }>({ line: 1, col: 1 });
  const [language, setLanguage] = useState<string>('TypeScript');
  const [encoding, setEncoding] = useState<string>('UTF-8');
  const [eol, setEol] = useState<string>('LF');
  
  useEffect(() => {
    // In a real implementation, this would get actual Git status
    // For now, we'll simulate it
    setGitBranch('main');
    setGitStatus('clean');
  }, [projectPath]);
  
  return (
    <div className="status-bar">
      <div className="status-left">
        <div className="status-item git-info">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"/>
          </svg>
          <span>{gitBranch}</span>
          {gitStatus !== 'clean' && (
            <span className="git-changes">*</span>
          )}
        </div>
        
        <div className="status-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="12" y1="8" x2="12" y2="12"/>
            <line x1="12" y1="16" x2="12.01" y2="16"/>
          </svg>
          <span>0 Problems</span>
        </div>
      </div>
      
      <div className="status-center">
        <div className="status-item mbah-ai">
          <div className="ai-indicator">
            <div className="ai-pulse"></div>
          </div>
          <span>Mbah Ai Ready</span>
        </div>
      </div>
      
      <div className="status-right">
        <div className="status-item cursor-position">
          <span>Ln {lineCol.line}, Col {lineCol.col}</span>
        </div>
        
        <div className="status-item language">
          <span>{language}</span>
        </div>
        
        <div className="status-item encoding">
          <span>{encoding}</span>
        </div>
        
        <div className="status-item eol">
          <span>{eol}</span>
        </div>
        
        <div className="status-item theme">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
          </svg>
          <span>Nusantara Glow</span>
        </div>
        
        {onToggleTerminal && (
          <div
            className={`status-item terminal-toggle ${isTerminalVisible ? 'active' : ''}`}
            onClick={onToggleTerminal}
            title={isTerminalVisible ? "Hide Terminal" : "Show Terminal"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="4 17 10 11 4 5"></polyline>
              <line x1="12" y1="19" x2="20" y2="19"></line>
            </svg>
          </div>
        )}

        <div className="status-item notifications">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
            <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default StatusBar;
