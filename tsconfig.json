{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/renderer/src/components/*"], "@/pages/*": ["src/renderer/src/pages/*"], "@/styles/*": ["src/renderer/src/styles/*"], "@/assets/*": ["src/renderer/src/assets/*"], "@/hooks/*": ["src/renderer/src/hooks/*"], "@/store/*": ["src/renderer/src/store/*"], "@/utils/*": ["src/renderer/src/utils/*"], "@/main/*": ["src/main/*"]}}, "include": ["src/**/*", "api/**/*", "ai/**/*"], "exclude": ["node_modules", "dist", "build"]}