/* Status Bar Styles */

.status-bar {
  height: 22px;
  background: var(--secondary-bg);
  border-top: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-sm);
  font-size: 11px;
  color: var(--text-secondary);
  user-select: none;
}

.status-left, .status-center, .status-right {
  display: flex;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 0 var(--spacing-sm);
  height: 100%;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.status-item:hover {
  background: var(--tertiary-bg);
}

.status-item svg {
  color: var(--text-muted);
}

/* Git Info */
.git-info {
  color: var(--text-secondary);
}

.git-changes {
  color: var(--warning);
}

/* Mbah Ai Indicator */
.mbah-ai {
  color: var(--neon-cyan);
}

.ai-indicator {
  position: relative;
  width: 8px;
  height: 8px;
}

.ai-pulse {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--neon-cyan);
  animation: ai-pulse 2s infinite;
}

@keyframes ai-pulse {
  0% {
    transform: scale(0.8);
    box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(0, 212, 255, 0);
  }

  100% {
    transform: scale(0.8);
  }
}

/* Terminal Toggle */
.terminal-toggle {
  position: relative;
}

.terminal-toggle.active {
  background: var(--tertiary-bg);
  color: var(--neon-cyan);
}

.terminal-toggle.active svg {
  color: var(--neon-cyan);
}

.terminal-toggle.active::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 2px;
  background: var(--neon-cyan);
  border-radius: 50%;
  box-shadow: 0 0 4px var(--neon-cyan);
}

/* Responsive Design */
@media (max-width: 768px) {
  .status-center {
    display: none;
  }
  
  .status-right {
    gap: var(--spacing-xs);
  }
  
  .status-item {
    padding: 0 var(--spacing-xs);
  }
  
  .encoding, .eol {
    display: none;
  }
}
