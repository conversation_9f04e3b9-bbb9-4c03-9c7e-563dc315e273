import React, { useState, useEffect, useRef } from 'react';
import { 
  MessageCircle, 
  Send, 
  Zap, 
  Settings, 
  Trash2, 
  Plus, 
  Copy, 
  ThumbsUp, 
  ThumbsDown,
  Code,
  FileText,
  Bug,
  TestTube,
  Lightbulb,
  Wifi,
  WifiOff
} from 'lucide-react';
import '../styles/AIAssistant.css';

// AI Message interface
interface AIMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    model?: string;
    provider?: string;
    tokens?: number;
    cost?: number;
  };
}

// AI Conversation interface
interface AIConversation {
  id: string;
  title: string;
  messages: AIMessage[];
  createdAt: Date;
  updatedAt: Date;
}

interface AIAssistantProps {
  isVisible: boolean;
  onClose: () => void;
  selectedCode?: string;
  filePath?: string;
  language?: string;
  cursorPosition?: { line: number; column: number };
}

const AIAssistant: React.FC<AIAssistantProps> = ({ 
  isVisible, 
  onClose, 
  selectedCode, 
  filePath, 
  language, 
  cursorPosition 
}) => {
  const [conversations, setConversations] = useState<AIConversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<AIConversation | null>(null);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isOnline, setIsOnline] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Load conversations on mount
  useEffect(() => {
    if (isVisible) {
      loadConversations();
      checkOnlineStatus();
    }
  }, [isVisible]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [activeConversation?.messages]);

  // Focus input when visible
  useEffect(() => {
    if (isVisible && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isVisible]);

  /**
   * Load conversations from AI service
   */
  const loadConversations = async () => {
    try {
      const convs = await window.electron.ai.getAllConversations();
      setConversations(convs);
      
      if (convs.length > 0 && !activeConversation) {
        setActiveConversation(convs[0]);
      }
    } catch (error) {
      console.error('Failed to load conversations:', error);
    }
  };

  /**
   * Check online status
   */
  const checkOnlineStatus = async () => {
    try {
      const config = await window.electron.ai.getConfiguration();
      setIsOnline(config.provider !== 'offline');
    } catch (error) {
      console.error('Failed to check online status:', error);
    }
  };

  /**
   * Create new conversation
   */
  const createNewConversation = async () => {
    try {
      const conversation = await window.electron.ai.createConversation();
      setConversations(prev => [conversation, ...prev]);
      setActiveConversation(conversation);
    } catch (error) {
      console.error('Failed to create conversation:', error);
    }
  };

  /**
   * Send message to AI
   */
  const sendMessage = async () => {
    if (!message.trim() || isLoading) return;

    try {
      setIsLoading(true);
      
      // Create conversation if none exists
      let conversation = activeConversation;
      if (!conversation) {
        conversation = await window.electron.ai.createConversation();
        setActiveConversation(conversation);
        setConversations(prev => [conversation, ...prev]);
      }

      // Build context
      const context = {
        filePath,
        language,
        selectedCode,
        cursorPosition
      };

      // Send message
      const response = await window.electron.ai.sendMessage(message, conversation.id, context);
      
      // Clear input
      setMessage('');
      
      // Reload conversation to get updated messages
      const updatedConversation = await window.electron.ai.getConversation(conversation.id);
      if (updatedConversation) {
        setActiveConversation(updatedConversation);
        
        // Update conversations list
        setConversations(prev => 
          prev.map(conv => conv.id === conversation.id ? updatedConversation : conv)
        );
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Delete conversation
   */
  const deleteConversation = async (conversationId: string) => {
    try {
      const success = await window.electron.ai.deleteConversation(conversationId);
      if (success) {
        setConversations(prev => prev.filter(conv => conv.id !== conversationId));
        
        if (activeConversation?.id === conversationId) {
          const remaining = conversations.filter(conv => conv.id !== conversationId);
          setActiveConversation(remaining.length > 0 ? remaining[0] : null);
        }
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
    }
  };

  /**
   * Copy message content
   */
  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    // TODO: Show toast notification
  };

  /**
   * Scroll to bottom of messages
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * Handle keyboard shortcuts
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      sendMessage();
    }
  };

  /**
   * Get quick action buttons
   */
  const getQuickActions = () => [
    {
      icon: <Code size={16} />,
      label: 'Explain Code',
      action: () => setMessage(selectedCode ? `Explain this code:\n\`\`\`${language}\n${selectedCode}\n\`\`\`` : 'Explain the current code'),
      disabled: !selectedCode
    },
    {
      icon: <Bug size={16} />,
      label: 'Find Bugs',
      action: () => setMessage(selectedCode ? `Find bugs in this code:\n\`\`\`${language}\n${selectedCode}\n\`\`\`` : 'Help me find bugs in my code'),
      disabled: !selectedCode
    },
    {
      icon: <TestTube size={16} />,
      label: 'Generate Tests',
      action: () => setMessage(selectedCode ? `Generate unit tests for this code:\n\`\`\`${language}\n${selectedCode}\n\`\`\`` : 'Help me generate unit tests'),
      disabled: !selectedCode
    },
    {
      icon: <Lightbulb size={16} />,
      label: 'Optimize',
      action: () => setMessage(selectedCode ? `Optimize this code:\n\`\`\`${language}\n${selectedCode}\n\`\`\`` : 'Help me optimize my code'),
      disabled: !selectedCode
    },
    {
      icon: <FileText size={16} />,
      label: 'Document',
      action: () => setMessage(selectedCode ? `Add documentation for this code:\n\`\`\`${language}\n${selectedCode}\n\`\`\`` : 'Help me document my code'),
      disabled: !selectedCode
    }
  ];

  /**
   * Format message content
   */
  const formatMessage = (content: string) => {
    // Simple markdown-like formatting
    return content
      .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n/g, '<br>');
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="ai-assistant">
      <div className="ai-assistant-header">
        <div className="ai-assistant-title">
          <Zap size={20} />
          <span>Mbah Ai Assistant</span>
          <div className="ai-status">
            {isOnline ? (
              <div className="ai-status-online">
                <Wifi size={14} />
                <span>Online</span>
              </div>
            ) : (
              <div className="ai-status-offline">
                <WifiOff size={14} />
                <span>Offline</span>
              </div>
            )}
          </div>
        </div>
        <div className="ai-assistant-actions">
          <button
            className="ai-action-btn"
            onClick={() => setShowSettings(!showSettings)}
            title="Settings"
          >
            <Settings size={16} />
          </button>
          <button
            className="ai-action-btn"
            onClick={createNewConversation}
            title="New Conversation"
          >
            <Plus size={16} />
          </button>
          <button
            className="ai-action-btn close-btn"
            onClick={onClose}
            title="Close"
          >
            ×
          </button>
        </div>
      </div>

      <div className="ai-assistant-content">
        {/* Conversations Sidebar */}
        <div className="ai-conversations">
          <div className="ai-conversations-header">
            <h3>Conversations</h3>
          </div>
          <div className="ai-conversations-list">
            {conversations.map(conversation => (
              <div
                key={conversation.id}
                className={`ai-conversation-item ${activeConversation?.id === conversation.id ? 'active' : ''}`}
                onClick={() => setActiveConversation(conversation)}
              >
                <div className="ai-conversation-info">
                  <span className="ai-conversation-title">{conversation.title}</span>
                  <span className="ai-conversation-date">
                    {new Date(conversation.updatedAt).toLocaleDateString()}
                  </span>
                </div>
                <button
                  className="ai-conversation-delete"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteConversation(conversation.id);
                  }}
                  title="Delete Conversation"
                >
                  <Trash2 size={12} />
                </button>
              </div>
            ))}
            
            {conversations.length === 0 && (
              <div className="ai-conversations-empty">
                <MessageCircle size={32} />
                <p>No conversations yet</p>
                <button className="btn btn-primary" onClick={createNewConversation}>
                  Start Chatting
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Chat Area */}
        <div className="ai-chat">
          {activeConversation ? (
            <>
              <div className="ai-messages">
                {activeConversation.messages.map(msg => (
                  <div key={msg.id} className={`ai-message ${msg.role}`}>
                    <div className="ai-message-avatar">
                      {msg.role === 'user' ? '👤' : '🧙‍♂️'}
                    </div>
                    <div className="ai-message-content">
                      <div className="ai-message-header">
                        <span className="ai-message-role">
                          {msg.role === 'user' ? 'You' : 'Mbah Ai'}
                        </span>
                        <span className="ai-message-time">
                          {new Date(msg.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <div 
                        className="ai-message-text"
                        dangerouslySetInnerHTML={{ __html: formatMessage(msg.content) }}
                      />
                      <div className="ai-message-actions">
                        <button
                          className="ai-message-action"
                          onClick={() => copyMessage(msg.content)}
                          title="Copy"
                        >
                          <Copy size={12} />
                        </button>
                        {msg.role === 'assistant' && (
                          <>
                            <button className="ai-message-action" title="Good response">
                              <ThumbsUp size={12} />
                            </button>
                            <button className="ai-message-action" title="Bad response">
                              <ThumbsDown size={12} />
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                
                {isLoading && (
                  <div className="ai-message assistant">
                    <div className="ai-message-avatar">🧙‍♂️</div>
                    <div className="ai-message-content">
                      <div className="ai-message-loading">
                        <div className="ai-typing-indicator">
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                        <span>Mbah Ai is thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>

              {/* Quick Actions */}
              {selectedCode && (
                <div className="ai-quick-actions">
                  <span className="ai-quick-actions-label">Quick Actions:</span>
                  <div className="ai-quick-actions-buttons">
                    {getQuickActions().map((action, index) => (
                      <button
                        key={index}
                        className="ai-quick-action"
                        onClick={action.action}
                        disabled={action.disabled}
                        title={action.label}
                      >
                        {action.icon}
                        <span>{action.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Input Area */}
              <div className="ai-input-area">
                <div className="ai-input-container">
                  <textarea
                    ref={inputRef}
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Ask Mbah Ai anything about your code..."
                    className="ai-input"
                    rows={3}
                    disabled={isLoading}
                  />
                  <button
                    className="ai-send-btn"
                    onClick={sendMessage}
                    disabled={!message.trim() || isLoading}
                    title="Send message (Ctrl+Enter)"
                  >
                    <Send size={16} />
                  </button>
                </div>
                
                {filePath && (
                  <div className="ai-context-info">
                    <span>Context: {filePath}</span>
                    {selectedCode && <span>• {selectedCode.split('\n').length} lines selected</span>}
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="ai-chat-empty">
              <Zap size={64} />
              <h2>Welcome to Mbah Ai</h2>
              <p>Your wise Indonesian AI coding assistant</p>
              <p>Start a new conversation to get help with your code!</p>
              <button className="btn btn-primary" onClick={createNewConversation}>
                <Plus size={16} />
                New Conversation
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="ai-settings-overlay" onClick={() => setShowSettings(false)}>
          <div className="ai-settings-panel" onClick={(e) => e.stopPropagation()}>
            <div className="ai-settings-header">
              <h3>AI Assistant Settings</h3>
              <button onClick={() => setShowSettings(false)}>×</button>
            </div>
            <div className="ai-settings-content">
              <p>Settings panel coming soon...</p>
              <p>Configure AI providers, models, and preferences here.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIAssistant;
