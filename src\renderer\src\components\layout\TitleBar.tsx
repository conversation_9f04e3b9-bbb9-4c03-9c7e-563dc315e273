import React from 'react';
import '../../styles/TitleBar.css';

interface TitleBarProps {
  projectName: string;
}

const TitleBar: React.FC<TitleBarProps> = ({ projectName }) => {
  return (
    <div className="title-bar">
      <div className="title-bar-left">
        <div className="app-logo">
          <div className="lightning-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M11 21h-1l1-7H7.5c-.58 0-.57-.32-.38-.66.19-.34.05-.08.07-.12C8.48 10.94 10.42 7.54 13 3h1l-1 7h3.5c.49 0 .56.33.47.51l-.07.15C12.96 17.55 11 21 11 21z"/>
            </svg>
          </div>
          <span className="app-name">KilatCode</span>
        </div>
        
        <div className="project-info">
          <span className="project-name">{projectName}</span>
        </div>
      </div>
      
      <div className="title-bar-center">
        <div className="menu-bar">
          <div className="menu-item">File</div>
          <div className="menu-item">Edit</div>
          <div className="menu-item">View</div>
          <div className="menu-item">Terminal</div>
          <div className="menu-item">Help</div>
        </div>
      </div>
      
      <div className="title-bar-right">
        <div className="window-controls">
          <button className="control-btn minimize">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="5" y1="12" x2="19" y2="12"/>
            </svg>
          </button>
          <button className="control-btn maximize">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
            </svg>
          </button>
          <button className="control-btn close">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TitleBar;
