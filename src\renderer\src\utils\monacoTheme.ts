import * as monaco from 'monaco-editor';

/**
 * Nusantara Glow theme for Monaco Editor
 * Cyberpunk-inspired dark theme with neon accents
 */
export const NUSANTARA_GLOW_THEME: monaco.editor.IStandaloneThemeData = {
  base: 'vs-dark',
  inherit: true,
  rules: [
    // Comments
    { token: 'comment', foreground: '888888', fontStyle: 'italic' },
    { token: 'comment.line', foreground: '888888', fontStyle: 'italic' },
    { token: 'comment.block', foreground: '888888', fontStyle: 'italic' },
    
    // Keywords
    { token: 'keyword', foreground: '00d4ff', fontStyle: 'bold' },
    { token: 'keyword.control', foreground: '00d4ff', fontStyle: 'bold' },
    { token: 'keyword.operator', foreground: '00ffff' },
    { token: 'keyword.other', foreground: '00d4ff' },
    
    // Strings
    { token: 'string', foreground: '00ff88' },
    { token: 'string.quoted', foreground: '00ff88' },
    { token: 'string.template', foreground: '00ff88' },
    { token: 'string.regexp', foreground: 'ff6b6b' },
    
    // Numbers
    { token: 'number', foreground: 'ffaa00' },
    { token: 'number.hex', foreground: 'ffaa00' },
    { token: 'number.binary', foreground: 'ffaa00' },
    { token: 'number.octal', foreground: 'ffaa00' },
    { token: 'number.float', foreground: 'ffaa00' },
    
    // Types and Classes
    { token: 'type', foreground: '8a2be2', fontStyle: 'bold' },
    { token: 'type.identifier', foreground: '8a2be2' },
    { token: 'class', foreground: '8a2be2', fontStyle: 'bold' },
    { token: 'class.identifier', foreground: '8a2be2' },
    { token: 'interface', foreground: '8a2be2', fontStyle: 'bold' },
    
    // Functions
    { token: 'function', foreground: '00d4ff' },
    { token: 'function.identifier', foreground: '00d4ff' },
    { token: 'method', foreground: '00d4ff' },
    { token: 'method.identifier', foreground: '00d4ff' },
    
    // Variables
    { token: 'variable', foreground: 'ffffff' },
    { token: 'variable.identifier', foreground: 'ffffff' },
    { token: 'variable.parameter', foreground: 'b8b8b8' },
    { token: 'variable.other', foreground: 'ffffff' },
    
    // Constants
    { token: 'constant', foreground: 'ffaa00', fontStyle: 'bold' },
    { token: 'constant.numeric', foreground: 'ffaa00' },
    { token: 'constant.language', foreground: 'ff6b6b', fontStyle: 'bold' },
    { token: 'constant.character', foreground: '00ff88' },
    
    // Operators
    { token: 'operator', foreground: '00ffff' },
    { token: 'delimiter', foreground: 'b8b8b8' },
    { token: 'delimiter.bracket', foreground: '00ffff' },
    { token: 'delimiter.parenthesis', foreground: '00ffff' },
    { token: 'delimiter.square', foreground: '00ffff' },
    { token: 'delimiter.angle', foreground: '00ffff' },
    
    // Tags (HTML/XML)
    { token: 'tag', foreground: '00d4ff', fontStyle: 'bold' },
    { token: 'tag.id', foreground: '8a2be2' },
    { token: 'tag.class', foreground: '00ff88' },
    { token: 'attribute.name', foreground: 'ffaa00' },
    { token: 'attribute.value', foreground: '00ff88' },
    
    // CSS
    { token: 'property', foreground: '00d4ff' },
    { token: 'property.value', foreground: '00ff88' },
    { token: 'selector', foreground: '8a2be2', fontStyle: 'bold' },
    { token: 'selector.class', foreground: '00ff88' },
    { token: 'selector.id', foreground: 'ffaa00' },
    
    // JSON
    { token: 'key', foreground: '00d4ff' },
    { token: 'value', foreground: '00ff88' },
    
    // Markdown
    { token: 'emphasis', foreground: '00ff88', fontStyle: 'italic' },
    { token: 'strong', foreground: '00d4ff', fontStyle: 'bold' },
    { token: 'header', foreground: '8a2be2', fontStyle: 'bold' },
    { token: 'link', foreground: '00d4ff', fontStyle: 'underline' },
    { token: 'code', foreground: 'ffaa00' },
    
    // Error and Warning
    { token: 'invalid', foreground: 'ff4444', fontStyle: 'bold' },
    { token: 'invalid.deprecated', foreground: 'ff6b6b', fontStyle: 'strikethrough' },
    
    // Special tokens
    { token: 'meta', foreground: 'b8b8b8' },
    { token: 'meta.tag', foreground: 'b8b8b8' },
    { token: 'meta.preprocessor', foreground: '8a2be2' },
    { token: 'annotation', foreground: '888888', fontStyle: 'italic' },
    
    // Language specific
    // TypeScript/JavaScript
    { token: 'support.type', foreground: '8a2be2' },
    { token: 'support.class', foreground: '8a2be2' },
    { token: 'support.function', foreground: '00d4ff' },
    { token: 'storage.type', foreground: '00d4ff', fontStyle: 'bold' },
    { token: 'storage.modifier', foreground: '00d4ff' },
    
    // Python
    { token: 'support.function.builtin', foreground: '00d4ff' },
    { token: 'support.type.python', foreground: '8a2be2' },
    
    // Rust
    { token: 'storage.type.rust', foreground: '00d4ff', fontStyle: 'bold' },
    { token: 'keyword.other.rust', foreground: '00d4ff' },
    
    // Go
    { token: 'storage.type.go', foreground: '00d4ff', fontStyle: 'bold' },
    { token: 'keyword.package.go', foreground: '8a2be2', fontStyle: 'bold' },
  ],
  colors: {
    // Editor background
    'editor.background': '#0a0a0a',
    'editor.foreground': '#ffffff',
    
    // Line numbers
    'editorLineNumber.foreground': '#888888',
    'editorLineNumber.activeForeground': '#00d4ff',
    
    // Cursor
    'editorCursor.foreground': '#00d4ff',
    'editorCursor.background': '#0a0a0a',
    
    // Selection
    'editor.selectionBackground': '#00d4ff33',
    'editor.selectionHighlightBackground': '#00d4ff22',
    'editor.inactiveSelectionBackground': '#00d4ff11',
    
    // Find/Replace
    'editor.findMatchBackground': '#ffaa0066',
    'editor.findMatchHighlightBackground': '#ffaa0033',
    'editor.findRangeHighlightBackground': '#00d4ff22',
    
    // Word highlight
    'editor.wordHighlightBackground': '#00d4ff22',
    'editor.wordHighlightStrongBackground': '#00d4ff33',
    
    // Brackets
    'editorBracketMatch.background': '#00ffff44',
    'editorBracketMatch.border': '#00ffff',
    
    // Indentation guides
    'editorIndentGuide.background': '#333333',
    'editorIndentGuide.activeBackground': '#00d4ff',
    
    // Whitespace
    'editorWhitespace.foreground': '#444444',
    
    // Rulers
    'editorRuler.foreground': '#333333',
    
    // Code lens
    'editorCodeLens.foreground': '#888888',
    
    // Gutter
    'editorGutter.background': '#0a0a0a',
    'editorGutter.modifiedBackground': '#ffaa00',
    'editorGutter.addedBackground': '#00ff88',
    'editorGutter.deletedBackground': '#ff4444',
    
    // Scrollbar
    'scrollbar.shadow': '#000000',
    'scrollbarSlider.background': '#33333366',
    'scrollbarSlider.hoverBackground': '#44444466',
    'scrollbarSlider.activeBackground': '#55555566',
    
    // Minimap
    'minimap.background': '#0a0a0a',
    'minimap.selectionHighlight': '#00d4ff33',
    'minimap.findMatchHighlight': '#ffaa0066',
    
    // Overview ruler
    'editorOverviewRuler.background': '#0a0a0a',
    'editorOverviewRuler.border': '#333333',
    'editorOverviewRuler.findMatchForeground': '#ffaa00',
    'editorOverviewRuler.selectionHighlightForeground': '#00d4ff',
    'editorOverviewRuler.wordHighlightForeground': '#00d4ff88',
    'editorOverviewRuler.wordHighlightStrongForeground': '#00d4ffaa',
    'editorOverviewRuler.modifiedForeground': '#ffaa00',
    'editorOverviewRuler.addedForeground': '#00ff88',
    'editorOverviewRuler.deletedForeground': '#ff4444',
    'editorOverviewRuler.errorForeground': '#ff4444',
    'editorOverviewRuler.warningForeground': '#ffaa00',
    'editorOverviewRuler.infoForeground': '#00d4ff',
    
    // Error/Warning squiggles
    'editorError.foreground': '#ff4444',
    'editorWarning.foreground': '#ffaa00',
    'editorInfo.foreground': '#00d4ff',
    'editorHint.foreground': '#888888',
    
    // Hover
    'editorHoverWidget.background': '#1a1a2e',
    'editorHoverWidget.border': '#00d4ff',
    'editorHoverWidget.foreground': '#ffffff',
    
    // Suggest widget
    'editorSuggestWidget.background': '#1a1a2e',
    'editorSuggestWidget.border': '#00d4ff',
    'editorSuggestWidget.foreground': '#ffffff',
    'editorSuggestWidget.selectedBackground': '#16213e',
    'editorSuggestWidget.highlightForeground': '#00d4ff',
    
    // Parameter hints
    'editorParameterHint.background': '#1a1a2e',
    'editorParameterHint.border': '#00d4ff',
    'editorParameterHint.foreground': '#ffffff',
  }
};

/**
 * Register the Nusantara Glow theme with Monaco
 */
export function registerNusantaraGlowTheme() {
  monaco.editor.defineTheme('nusantara-glow', NUSANTARA_GLOW_THEME);
}

/**
 * Apply theme based on user preference
 */
export function applyTheme(themeName: string = 'nusantara-glow') {
  monaco.editor.setTheme(themeName);
}
