import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import { DatabaseManager } from './DatabaseManager';

// AI Provider types
export enum AIProvider {
  OFFLINE = 'offline',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GOOGLE = 'google',
  OLLAMA = 'ollama'
}

// AI Message types
export interface AIMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    model?: string;
    provider?: AIProvider;
    tokens?: number;
    cost?: number;
  };
}

// AI Conversation
export interface AIConversation {
  id: string;
  title: string;
  messages: AIMessage[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: {
    model?: string;
    provider?: AIProvider;
    context?: string;
  };
}

// AI Configuration
export interface AIConfig {
  provider: AIProvider;
  model: string;
  apiKey?: string;
  baseUrl?: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  offlineModel?: {
    path: string;
    loaded: boolean;
  };
}

// AI Capabilities
export interface AICapabilities {
  codeCompletion: boolean;
  codeExplanation: boolean;
  codeGeneration: boolean;
  testGeneration: boolean;
  bugDetection: boolean;
  refactoring: boolean;
  documentation: boolean;
  translation: boolean;
  chat: boolean;
}

// AI Response
export interface AIResponse {
  content: string;
  model: string;
  provider: AIProvider;
  tokens?: number;
  cost?: number;
  error?: string;
}

/**
 * Mbah Ai Assistant Service
 * Provides AI-powered coding assistance with offline and online capabilities
 */
export class AIAssistantService extends EventEmitter {
  private db: DatabaseManager;
  private config: AIConfig;
  private conversations: Map<string, AIConversation> = new Map();
  private isInitialized = false;
  private offlineModel: any = null;

  constructor(db: DatabaseManager) {
    super();
    this.db = db;
    
    // Default configuration
    this.config = {
      provider: AIProvider.OFFLINE,
      model: 'mbah-ai-local',
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: `You are Mbah Ai, a wise and helpful Indonesian AI coding assistant. 
You are integrated into KilatCode IDE and help developers with:
- Code completion and suggestions
- Code explanation and documentation
- Bug detection and fixing
- Test generation
- Code refactoring
- General programming questions

Always provide helpful, accurate, and contextual responses. 
Use Indonesian terms when appropriate, but keep code examples in English.
Be friendly and encouraging, like a wise mentor (mbah means grandfather/elder in Javanese).`,
    };
  }

  /**
   * Initialize AI Assistant Service
   */
  public async initialize(): Promise<void> {
    try {
      // Load configuration from database
      await this.loadConfiguration();
      
      // Load conversations from database
      await this.loadConversations();
      
      // Initialize AI provider
      await this.initializeProvider();
      
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize AI Assistant:', error instanceof Error ? error.message : String(error));
      this.emit('error', error);
    }
  }

  /**
   * Load configuration from database
   */
  private async loadConfiguration(): Promise<void> {
    try {
      const savedConfig = await this.db.get('settings', 'ai-config');
      if (savedConfig) {
        this.config = { ...this.config, ...savedConfig.value };
      }
    } catch (error) {
      console.warn('Failed to load AI configuration:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Save configuration to database
   */
  private async saveConfiguration(): Promise<void> {
    try {
      await this.db.set('settings', 'ai-config', {
        key: 'ai-config',
        value: this.config,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Failed to save AI configuration:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Load conversations from database
   */
  private async loadConversations(): Promise<void> {
    try {
      const conversations = await this.db.getAll('ai_conversations');
      for (const conv of conversations) {
        this.conversations.set(conv.id, conv);
      }
    } catch (error) {
      console.warn('Failed to load AI conversations:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Initialize AI provider
   */
  private async initializeProvider(): Promise<void> {
    switch (this.config.provider) {
      case AIProvider.OFFLINE:
        await this.initializeOfflineProvider();
        break;
      case AIProvider.OPENAI:
        await this.initializeOpenAIProvider();
        break;
      case AIProvider.ANTHROPIC:
        await this.initializeAnthropicProvider();
        break;
      case AIProvider.GOOGLE:
        await this.initializeGoogleProvider();
        break;
      case AIProvider.OLLAMA:
        await this.initializeOllamaProvider();
        break;
      default:
        throw new Error(`Unsupported AI provider: ${this.config.provider}`);
    }
  }

  /**
   * Initialize offline AI provider
   */
  private async initializeOfflineProvider(): Promise<void> {
    try {
      // For now, we'll use a simple rule-based system
      // In the future, this could load a local model like GGML or ONNX
      console.log('Initializing offline AI provider...');
      
      // Check if offline model exists
      const modelPath = path.join(app.getPath('userData'), 'ai-models', 'mbah-ai.bin');
      
      if (fs.existsSync(modelPath)) {
        // Load offline model (placeholder)
        this.offlineModel = {
          loaded: true,
          path: modelPath
        };
        console.log('Offline AI model loaded successfully');
      } else {
        console.log('No offline AI model found, using rule-based responses');
        this.offlineModel = {
          loaded: false,
          path: modelPath
        };
      }
    } catch (error) {
      console.error('Failed to initialize offline AI provider:', error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Initialize OpenAI provider
   */
  private async initializeOpenAIProvider(): Promise<void> {
    if (!this.config.apiKey) {
      throw new Error('OpenAI API key is required');
    }
    
    // Validate API key
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`OpenAI API validation failed: ${response.statusText}`);
      }
      
      console.log('OpenAI provider initialized successfully');
    } catch (error) {
      console.error('Failed to initialize OpenAI provider:', error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Initialize Anthropic provider
   */
  private async initializeAnthropicProvider(): Promise<void> {
    if (!this.config.apiKey) {
      throw new Error('Anthropic API key is required');
    }
    
    console.log('Anthropic provider initialized successfully');
  }

  /**
   * Initialize Google provider
   */
  private async initializeGoogleProvider(): Promise<void> {
    if (!this.config.apiKey) {
      throw new Error('Google API key is required');
    }
    
    console.log('Google provider initialized successfully');
  }

  /**
   * Initialize Ollama provider
   */
  private async initializeOllamaProvider(): Promise<void> {
    const baseUrl = this.config.baseUrl || 'http://localhost:11434';
    
    try {
      const response = await fetch(`${baseUrl}/api/tags`);
      if (!response.ok) {
        throw new Error(`Ollama server not available at ${baseUrl}`);
      }
      
      console.log('Ollama provider initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Ollama provider:', error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Send message to AI and get response
   */
  public async sendMessage(
    message: string, 
    conversationId?: string,
    context?: {
      filePath?: string;
      language?: string;
      selectedCode?: string;
      cursorPosition?: { line: number; column: number };
    }
  ): Promise<AIResponse> {
    try {
      if (!this.isInitialized) {
        throw new Error('AI Assistant not initialized');
      }

      // Get or create conversation
      let conversation: AIConversation;
      if (conversationId && this.conversations.has(conversationId)) {
        conversation = this.conversations.get(conversationId)!;
      } else {
        conversation = await this.createConversation();
      }

      // Add user message
      const userMessage: AIMessage = {
        id: this.generateMessageId(),
        role: 'user',
        content: message,
        timestamp: new Date()
      };
      
      conversation.messages.push(userMessage);

      // Generate AI response based on provider
      let response: AIResponse;
      switch (this.config.provider) {
        case AIProvider.OFFLINE:
          response = await this.generateOfflineResponse(message, conversation, context);
          break;
        case AIProvider.OPENAI:
          response = await this.generateOpenAIResponse(message, conversation, context);
          break;
        case AIProvider.ANTHROPIC:
          response = await this.generateAnthropicResponse(message, conversation, context);
          break;
        case AIProvider.GOOGLE:
          response = await this.generateGoogleResponse(message, conversation, context);
          break;
        case AIProvider.OLLAMA:
          response = await this.generateOllamaResponse(message, conversation, context);
          break;
        default:
          throw new Error(`Unsupported AI provider: ${this.config.provider}`);
      }

      // Add AI response to conversation
      const aiMessage: AIMessage = {
        id: this.generateMessageId(),
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        metadata: {
          model: response.model,
          provider: response.provider,
          tokens: response.tokens,
          cost: response.cost
        }
      };
      
      conversation.messages.push(aiMessage);
      conversation.updatedAt = new Date();

      // Save conversation
      await this.saveConversation(conversation);

      this.emit('message-sent', { conversation, userMessage, aiMessage });
      
      return response;
    } catch (error) {
      console.error('Failed to send message to AI:', error instanceof Error ? error.message : String(error));
      
      return {
        content: `Maaf, terjadi kesalahan saat berkomunikasi dengan Mbah Ai: ${error instanceof Error ? error.message : String(error)}`,
        model: this.config.model,
        provider: this.config.provider,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Generate offline AI response
   */
  private async generateOfflineResponse(
    message: string, 
    conversation: AIConversation,
    context?: any
  ): Promise<AIResponse> {
    // Simple rule-based responses for offline mode
    const lowerMessage = message.toLowerCase();
    
    let response = '';
    
    if (lowerMessage.includes('hello') || lowerMessage.includes('hai') || lowerMessage.includes('halo')) {
      response = 'Halo! Saya Mbah Ai, asisten coding Anda. Ada yang bisa saya bantu hari ini?';
    } else if (lowerMessage.includes('help') || lowerMessage.includes('bantuan')) {
      response = `Saya bisa membantu Anda dengan:
- Menjelaskan kode
- Membuat dokumentasi
- Mencari bug
- Membuat unit test
- Refactoring kode
- Memberikan saran coding

Silakan tanyakan apa yang Anda butuhkan!`;
    } else if (lowerMessage.includes('explain') || lowerMessage.includes('jelaskan')) {
      if (context?.selectedCode) {
        response = `Kode yang Anda pilih:
\`\`\`${context.language || 'javascript'}
${context.selectedCode}
\`\`\`

Ini adalah kode ${context.language || 'JavaScript'} yang ${this.analyzeCode(context.selectedCode)}. 

Apakah ada bagian tertentu yang ingin saya jelaskan lebih detail?`;
      } else {
        response = 'Silakan pilih kode yang ingin dijelaskan, lalu tanyakan lagi kepada saya.';
      }
    } else if (lowerMessage.includes('test') || lowerMessage.includes('unit test')) {
      response = `Untuk membuat unit test yang baik:

1. **Test satu fungsi per test case**
2. **Gunakan naming yang deskriptif**
3. **Follow AAA pattern**: Arrange, Act, Assert
4. **Test edge cases dan error conditions**

Contoh struktur test:
\`\`\`javascript
describe('functionName', () => {
  it('should return expected result when given valid input', () => {
    // Arrange
    const input = 'test';
    
    // Act
    const result = functionName(input);
    
    // Assert
    expect(result).toBe('expected');
  });
});
\`\`\`

Apakah Anda ingin saya buatkan test untuk kode tertentu?`;
    } else if (lowerMessage.includes('bug') || lowerMessage.includes('error')) {
      response = `Untuk debugging yang efektif:

1. **Baca error message dengan teliti**
2. **Check console/logs untuk detail error**
3. **Isolate masalah dengan console.log**
4. **Gunakan debugger atau breakpoints**
5. **Cek typo dan syntax errors**

Jika Anda punya error spesifik, share kodenya dan saya akan bantu analisis!`;
    } else if (lowerMessage.includes('refactor') || lowerMessage.includes('clean code')) {
      response = `Prinsip clean code yang penting:

1. **Meaningful names** - gunakan nama yang jelas
2. **Small functions** - satu fungsi satu tanggung jawab
3. **DRY principle** - Don't Repeat Yourself
4. **Comments** - jelaskan WHY, bukan WHAT
5. **Consistent formatting** - gunakan prettier/eslint

Contoh refactoring:
\`\`\`javascript
// Before
function calc(a, b, op) {
  if (op === '+') return a + b;
  if (op === '-') return a - b;
}

// After
function calculate(firstNumber, secondNumber, operation) {
  const operations = {
    '+': (a, b) => a + b,
    '-': (a, b) => a - b
  };
  
  return operations[operation](firstNumber, secondNumber);
}
\`\`\``;
    } else {
      response = `Terima kasih atas pertanyaannya! Sebagai Mbah Ai dalam mode offline, saya bisa membantu dengan:

- Menjelaskan konsep programming
- Memberikan contoh kode
- Tips debugging dan best practices
- Struktur project dan arsitektur

Untuk respons yang lebih canggih, Anda bisa mengaktifkan mode online di pengaturan AI.

Apa yang spesifik ingin Anda tanyakan?`;
    }

    return {
      content: response,
      model: 'mbah-ai-offline',
      provider: AIProvider.OFFLINE
    };
  }

  /**
   * Generate OpenAI response
   */
  private async generateOpenAIResponse(
    message: string, 
    conversation: AIConversation,
    context?: any
  ): Promise<AIResponse> {
    try {
      const messages = [
        { role: 'system', content: this.config.systemPrompt },
        ...conversation.messages.slice(-10).map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        { role: 'user', content: this.buildContextualMessage(message, context) }
      ];

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.config.model || 'gpt-3.5-turbo',
          messages,
          temperature: this.config.temperature,
          max_tokens: this.config.maxTokens
        })
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        content: data.choices[0].message.content,
        model: data.model,
        provider: AIProvider.OPENAI,
        tokens: data.usage?.total_tokens,
        cost: this.calculateOpenAICost(data.usage?.total_tokens, data.model)
      };
    } catch (error) {
      throw new Error(`OpenAI request failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generate Anthropic response
   */
  private async generateAnthropicResponse(
    message: string, 
    conversation: AIConversation,
    context?: any
  ): Promise<AIResponse> {
    // Placeholder for Anthropic implementation
    throw new Error('Anthropic provider not yet implemented');
  }

  /**
   * Generate Google response
   */
  private async generateGoogleResponse(
    message: string, 
    conversation: AIConversation,
    context?: any
  ): Promise<AIResponse> {
    // Placeholder for Google implementation
    throw new Error('Google provider not yet implemented');
  }

  /**
   * Generate Ollama response
   */
  private async generateOllamaResponse(
    message: string, 
    conversation: AIConversation,
    context?: any
  ): Promise<AIResponse> {
    try {
      const baseUrl = this.config.baseUrl || 'http://localhost:11434';
      
      const response = await fetch(`${baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.config.model || 'codellama',
          prompt: this.buildContextualMessage(message, context),
          system: this.config.systemPrompt,
          stream: false,
          options: {
            temperature: this.config.temperature,
            num_predict: this.config.maxTokens
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        content: data.response,
        model: this.config.model || 'codellama',
        provider: AIProvider.OLLAMA
      };
    } catch (error) {
      throw new Error(`Ollama request failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Build contextual message with code context
   */
  private buildContextualMessage(message: string, context?: any): string {
    let contextualMessage = message;
    
    if (context) {
      if (context.filePath) {
        contextualMessage += `\n\nFile: ${context.filePath}`;
      }
      
      if (context.language) {
        contextualMessage += `\nLanguage: ${context.language}`;
      }
      
      if (context.selectedCode) {
        contextualMessage += `\n\nSelected code:\n\`\`\`${context.language || ''}\n${context.selectedCode}\n\`\`\``;
      }
      
      if (context.cursorPosition) {
        contextualMessage += `\nCursor position: Line ${context.cursorPosition.line}, Column ${context.cursorPosition.column}`;
      }
    }
    
    return contextualMessage;
  }

  /**
   * Analyze code for offline responses
   */
  private analyzeCode(code: string): string {
    if (code.includes('function')) {
      return 'mendefinisikan sebuah fungsi';
    } else if (code.includes('class')) {
      return 'mendefinisikan sebuah class';
    } else if (code.includes('if') || code.includes('else')) {
      return 'menggunakan conditional logic';
    } else if (code.includes('for') || code.includes('while')) {
      return 'menggunakan loop/perulangan';
    } else if (code.includes('import') || code.includes('require')) {
      return 'mengimport module atau library';
    } else {
      return 'melakukan operasi tertentu';
    }
  }

  /**
   * Calculate OpenAI cost
   */
  private calculateOpenAICost(tokens?: number, model?: string): number {
    if (!tokens) return 0;
    
    // Simplified cost calculation (actual rates may vary)
    const rates: Record<string, number> = {
      'gpt-3.5-turbo': 0.002 / 1000, // $0.002 per 1K tokens
      'gpt-4': 0.03 / 1000, // $0.03 per 1K tokens
      'gpt-4-turbo': 0.01 / 1000 // $0.01 per 1K tokens
    };
    
    const rate = rates[model || 'gpt-3.5-turbo'] || rates['gpt-3.5-turbo'];
    return tokens * rate;
  }

  /**
   * Create new conversation
   */
  public async createConversation(title?: string): Promise<AIConversation> {
    const conversation: AIConversation = {
      id: this.generateConversationId(),
      title: title || `Conversation ${new Date().toLocaleString()}`,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        model: this.config.model,
        provider: this.config.provider
      }
    };

    this.conversations.set(conversation.id, conversation);
    await this.saveConversation(conversation);
    
    this.emit('conversation-created', conversation);
    return conversation;
  }

  /**
   * Get conversation by ID
   */
  public getConversation(id: string): AIConversation | undefined {
    return this.conversations.get(id);
  }

  /**
   * Get all conversations
   */
  public getAllConversations(): AIConversation[] {
    return Array.from(this.conversations.values()).sort((a, b) => 
      b.updatedAt.getTime() - a.updatedAt.getTime()
    );
  }

  /**
   * Delete conversation
   */
  public async deleteConversation(id: string): Promise<boolean> {
    try {
      this.conversations.delete(id);
      await this.db.delete('ai_conversations', id);
      
      this.emit('conversation-deleted', id);
      return true;
    } catch (error) {
      console.error('Failed to delete conversation:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Save conversation to database
   */
  private async saveConversation(conversation: AIConversation): Promise<void> {
    try {
      await this.db.set('ai_conversations', conversation.id, conversation);
    } catch (error) {
      console.error('Failed to save conversation:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Update AI configuration
   */
  public async updateConfiguration(config: Partial<AIConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
    await this.saveConfiguration();
    
    // Reinitialize provider if provider changed
    if (config.provider || config.apiKey || config.baseUrl) {
      await this.initializeProvider();
    }
    
    this.emit('config-updated', this.config);
  }

  /**
   * Get current configuration
   */
  public getConfiguration(): AIConfig {
    return { ...this.config };
  }

  /**
   * Get AI capabilities
   */
  public getCapabilities(): AICapabilities {
    return {
      codeCompletion: true,
      codeExplanation: true,
      codeGeneration: true,
      testGeneration: true,
      bugDetection: true,
      refactoring: true,
      documentation: true,
      translation: true,
      chat: true
    };
  }

  /**
   * Generate unique conversation ID
   */
  private generateConversationId(): string {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Dispose service
   */
  public dispose(): void {
    this.conversations.clear();
    this.removeAllListeners();
  }
}
