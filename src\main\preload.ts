import { contextBridge, ipc<PERSON>enderer, shell } from 'electron';
import * as os from 'os';
import * as path from 'path';
import * as fs from 'fs';

interface FileInfo {
  name: string;
  isDirectory: boolean;
  path: string;
}

interface StatInfo {
  size: number;
  isFile: boolean;
  isDirectory: boolean;
  created: Date;
  modified: Date;
}

interface OsInfo {
  platform: string;
  release: string;
  arch: string;
  cpus: os.CpuInfo[];
  totalMemory: number;
  freeMemory: number;
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electron', {
  // App info
  appInfo: {
    getVersion: (): Promise<string> => ipcRenderer.invoke('app-version'),
    getPlatform: (): string => process.platform,
    getOsInfo: (): OsInfo => ({
      platform: os.platform(),
      release: os.release(),
      arch: os.arch(),
      cpus: os.cpus(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem()
    })
  },
  
  // File system operations (enhanced with new service methods)
  fileSystem: {
    // Basic file operations
    readFile: (filePath: string): Promise<string> => ipcRenderer.invoke('fs:read-file', filePath),
    writeFile: (filePath: string, content: string): Promise<void> => ipcRenderer.invoke('fs:write-file', filePath, content),
    readDirectory: (dirPath: string, recursive?: boolean): Promise<any[]> => ipcRenderer.invoke('fs:read-directory', dirPath, recursive),
    createFile: (filePath: string, content?: string): Promise<void> => ipcRenderer.invoke('fs:create-file', filePath, content),
    createDirectory: (dirPath: string): Promise<void> => ipcRenderer.invoke('fs:create-directory', dirPath),
    renameFile: (oldPath: string, newPath: string): Promise<void> => ipcRenderer.invoke('fs:rename-file', oldPath, newPath),
    deleteFile: (filePath: string): Promise<void> => ipcRenderer.invoke('fs:delete-file', filePath),
    deleteDirectory: (dirPath: string, recursive?: boolean): Promise<void> => ipcRenderer.invoke('fs:delete-directory', dirPath, recursive),

    // Legacy methods for backward compatibility
    readDir: (dirPath: string): Promise<FileInfo[]> => {
      return new Promise((resolve, reject) => {
        fs.readdir(dirPath, { withFileTypes: true }, (err, files) => {
          if (err) {
            reject(err);
            return;
          }

          const fileInfos: FileInfo[] = files.map(file => ({
            name: file.name,
            isDirectory: file.isDirectory(),
            path: path.join(dirPath, file.name)
          }));

          resolve(fileInfos);
        });
      });
    },
    stat: (filePath: string): Promise<StatInfo> => {
      return new Promise((resolve, reject) => {
        fs.stat(filePath, (err, stats) => {
          if (err) {
            reject(err);
            return;
          }
          resolve({
            size: stats.size,
            isFile: stats.isFile(),
            isDirectory: stats.isDirectory(),
            created: stats.birthtime,
            modified: stats.mtime
          });
        });
      });
    },
    exists: (filePath: string): Promise<boolean> => {
      return new Promise((resolve) => {
        fs.access(filePath, fs.constants.F_OK, (err) => {
          resolve(!err);
        });
      });
    },
    mkdir: (dirPath: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        fs.mkdir(dirPath, { recursive: true }, (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    },
    rename: (oldPath: string, newPath: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        fs.rename(oldPath, newPath, (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    },
    unlink: (filePath: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        fs.unlink(filePath, (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    },
    rmdir: (dirPath: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        fs.rm(dirPath, { recursive: true, force: true }, (err) => {
          if (err) {
            reject(err);
            return;
          }
          resolve();
        });
      });
    },
    watch: (filePath: string, callback: (eventType: string, filename: string | null) => void): (() => void) => {
      const watcher = fs.watch(filePath, { recursive: true }, (eventType, filename) => {
        callback(eventType, filename);
      });

      return () => {
        watcher.close();
      };
    }
  },

  // Database operations
  database: {
    getRecentProjects: (): Promise<any[]> => ipcRenderer.invoke('db:get-recent-projects'),
    getSettings: (category: string): Promise<any[]> => ipcRenderer.invoke('db:get-settings', category),
    setSetting: (key: string, value: string, category: string): Promise<void> => ipcRenderer.invoke('db:set-setting', key, value, category)
  },

  // Project management
  project: {
    open: (projectPath: string): Promise<any> => ipcRenderer.invoke('project:open', projectPath),
    close: (): Promise<void> => ipcRenderer.invoke('project:close'),
    create: (projectPath: string, template?: any): Promise<any> => ipcRenderer.invoke('project:create', projectPath, template),
    getCurrent: (): Promise<any> => ipcRenderer.invoke('project:get-current'),
    analyze: (projectPath: string): Promise<any> => ipcRenderer.invoke('project:analyze', projectPath)
  },

  // Dialog operations
  dialog: {
    openDirectory: (): Promise<any> => ipcRenderer.invoke('dialog:openDirectory'),
    openFile: (options?: any): Promise<any> => ipcRenderer.invoke('dialog:openFile', options),
    saveFile: (options?: any): Promise<any> => ipcRenderer.invoke('dialog:saveFile', options)
  },

  // Terminal operations
  terminal: {
    create: (options?: any): Promise<any> => ipcRenderer.invoke('terminal:create', options),
    write: (id: number, data: string): Promise<boolean> => ipcRenderer.invoke('terminal:write', id, data),
    resize: (id: number, cols: number, rows: number): Promise<boolean> => ipcRenderer.invoke('terminal:resize', id, cols, rows),
    kill: (id: number): Promise<boolean> => ipcRenderer.invoke('terminal:kill', id),
    getAll: (): Promise<any[]> => ipcRenderer.invoke('terminal:get-all'),
    get: (id: number): Promise<any> => ipcRenderer.invoke('terminal:get', id)
  },

  // Extension operations
  extensions: {
    getAll: (): Promise<any[]> => ipcRenderer.invoke('extensions:get-all'),
    get: (id: string): Promise<any> => ipcRenderer.invoke('extensions:get', id),
    install: (filePath: string): Promise<string | null> => ipcRenderer.invoke('extensions:install', filePath),
    uninstall: (id: string): Promise<boolean> => ipcRenderer.invoke('extensions:uninstall', id),
    enable: (id: string): Promise<boolean> => ipcRenderer.invoke('extensions:enable', id),
    disable: (id: string): Promise<boolean> => ipcRenderer.invoke('extensions:disable', id),
    getEnabled: (): Promise<any[]> => ipcRenderer.invoke('extensions:get-enabled')
  },

  // AI Assistant operations
  ai: {
    sendMessage: (message: string, conversationId?: string, context?: any): Promise<any> =>
      ipcRenderer.invoke('ai:send-message', message, conversationId, context),
    createConversation: (title?: string): Promise<any> =>
      ipcRenderer.invoke('ai:create-conversation', title),
    getConversation: (id: string): Promise<any> =>
      ipcRenderer.invoke('ai:get-conversation', id),
    getAllConversations: (): Promise<any[]> =>
      ipcRenderer.invoke('ai:get-all-conversations'),
    deleteConversation: (id: string): Promise<boolean> =>
      ipcRenderer.invoke('ai:delete-conversation', id),
    getConfiguration: (): Promise<any> =>
      ipcRenderer.invoke('ai:get-configuration'),
    updateConfiguration: (config: any): Promise<void> =>
      ipcRenderer.invoke('ai:update-configuration', config),
    getCapabilities: (): Promise<any> =>
      ipcRenderer.invoke('ai:get-capabilities')
  },

  // Visual Editor operations
  visual: {
    createProject: (name: string, path: string, framework: string): Promise<any> =>
      ipcRenderer.invoke('visual:create-project', name, path, framework),
    openProject: (projectId: string): Promise<any> =>
      ipcRenderer.invoke('visual:open-project', projectId),
    getAllProjects: (): Promise<any[]> =>
      ipcRenderer.invoke('visual:get-all-projects'),
    getActiveProject: (): Promise<any> =>
      ipcRenderer.invoke('visual:get-active-project'),
    deleteProject: (projectId: string): Promise<boolean> =>
      ipcRenderer.invoke('visual:delete-project', projectId)
  },

  // API Client operations
  api: {
    sendRequest: (request: any): Promise<any> =>
      ipcRenderer.invoke('api:send-request', request),
    createCollection: (name: string, description?: string): Promise<any> =>
      ipcRenderer.invoke('api:create-collection', name, description),
    createRequest: (collectionId: string, name: string, method?: string, url?: string): Promise<any> =>
      ipcRenderer.invoke('api:create-request', collectionId, name, method, url),
    getAllCollections: (): Promise<any[]> =>
      ipcRenderer.invoke('api:get-all-collections'),
    getCollection: (id: string): Promise<any> =>
      ipcRenderer.invoke('api:get-collection', id),
    deleteCollection: (collectionId: string): Promise<boolean> =>
      ipcRenderer.invoke('api:delete-collection', collectionId),
    updateRequest: (request: any): Promise<void> =>
      ipcRenderer.invoke('api:update-request', request),
    deleteRequest: (requestId: string): Promise<boolean> =>
      ipcRenderer.invoke('api:delete-request', requestId),
    getAllEnvironments: (): Promise<any[]> =>
      ipcRenderer.invoke('api:get-all-environments'),
    getActiveEnvironment: (): Promise<any> =>
      ipcRenderer.invoke('api:get-active-environment'),
    createEnvironment: (name: string): Promise<any> =>
      ipcRenderer.invoke('api:create-environment', name),
    setActiveEnvironment: (environmentId: string): Promise<void> =>
      ipcRenderer.invoke('api:set-active-environment', environmentId),
    getHistory: (limit?: number): Promise<any[]> =>
      ipcRenderer.invoke('api:get-history', limit)
  },

  // Shell operations
  shell: {
    openExternal: (url: string): Promise<void> => shell.openExternal(url)
  },

  // IPC communication
  ipc: {
    send: (channel: string, data?: any): void => {
      ipcRenderer.send(channel, data);
    },
    on: (channel: string, callback: (...args: any[]) => void): (() => void) => {
      // Deliberately strip event as it includes `sender` 
      const listener = (_event: any, ...args: any[]) => callback(...args);
      ipcRenderer.on(channel, listener);
      
      // Return a function to remove the listener
      return () => {
        ipcRenderer.removeListener(channel, listener);
      };
    },
    once: (channel: string, callback: (...args: any[]) => void): void => {
      ipcRenderer.once(channel, (_event, ...args) => callback(...args));
    },
    invoke: (channel: string, ...args: any[]): Promise<any> => {
      return ipcRenderer.invoke(channel, ...args);
    }
  },
  
  // Path utilities
  path: {
    join: (...args: string[]): string => path.join(...args),
    dirname: (p: string): string => path.dirname(p),
    basename: (p: string, ext?: string): string => path.basename(p, ext),
    extname: (p: string): string => path.extname(p),
    resolve: (...args: string[]): string => path.resolve(...args),
    isAbsolute: (p: string): boolean => path.isAbsolute(p)
  }
});
