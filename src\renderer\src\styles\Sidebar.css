/* Sidebar Styles */

.sidebar {
  display: flex;
  height: 100%;
  background: var(--secondary-bg);
  border-right: 1px solid var(--border-primary);
}

.sidebar-tabs {
  width: 48px;
  background: var(--primary-bg);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  padding: var(--spacing-sm) 0;
  gap: var(--spacing-xs);
}

.sidebar-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin: 0 var(--spacing-xs);
  border: none;
  background: transparent;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  position: relative;
}

.sidebar-tab:hover {
  background: var(--tertiary-bg);
  color: var(--text-secondary);
}

.sidebar-tab.active {
  background: var(--tertiary-bg);
  color: var(--neon-cyan);
  box-shadow: inset 2px 0 0 var(--neon-cyan);
}

.sidebar-tab.active::before {
  content: '';
  position: absolute;
  left: -1px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--neon-cyan);
  box-shadow: 0 0 5px var(--neon-cyan);
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-primary);
  background: var(--secondary-bg);
}

.tab-header h3 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* Search Tab */
.search-container {
  padding: var(--spacing-md);
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  background: var(--primary-bg);
  color: var(--text-primary);
  font-size: 12px;
  margin-bottom: var(--spacing-md);
}

.search-input:focus {
  border-color: var(--neon-cyan);
  box-shadow: 0 0 0 1px rgba(0, 212, 255, 0.2);
}

.search-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.search-options label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
}

.search-options input[type="checkbox"] {
  accent-color: var(--neon-cyan);
}

/* Git Tab */
.git-status {
  padding: var(--spacing-md);
  color: var(--text-secondary);
  font-size: 12px;
}

/* Extensions Tab */
.extensions-list {
  padding: var(--spacing-md);
  color: var(--text-secondary);
  font-size: 12px;
}

/* AI Tab */
.ai-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-md);
}

.ai-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-lg);
  gap: var(--spacing-md);
}

.ai-avatar {
  font-size: 32px;
  margin-bottom: var(--spacing-sm);
}

.ai-welcome p {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.ai-input {
  margin-top: auto;
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.ai-input-field {
  flex: 1;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  background: var(--primary-bg);
  color: var(--text-primary);
  font-size: 12px;
}

.ai-input-field:focus {
  border-color: var(--neon-cyan);
  box-shadow: 0 0 0 1px rgba(0, 212, 255, 0.2);
}

.ai-send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  background: var(--neon-cyan);
  color: var(--primary-bg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.ai-send-btn:hover {
  background: var(--neon-teal);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}
