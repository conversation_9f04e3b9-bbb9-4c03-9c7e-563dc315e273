/**
 * Language detection utility for Monaco Editor
 * Maps file extensions to Monaco Editor language identifiers
 */

export interface LanguageInfo {
  id: string;
  name: string;
  extensions: string[];
  aliases?: string[];
  mimeTypes?: string[];
}

export const SUPPORTED_LANGUAGES: LanguageInfo[] = [
  // Web Technologies
  {
    id: 'javascript',
    name: 'JavaScript',
    extensions: ['.js', '.mjs', '.cjs'],
    aliases: ['js'],
    mimeTypes: ['text/javascript', 'application/javascript'],
  },
  {
    id: 'typescript',
    name: 'TypeScript',
    extensions: ['.ts', '.tsx'],
    aliases: ['ts'],
    mimeTypes: ['text/typescript'],
  },
  {
    id: 'html',
    name: 'HTML',
    extensions: ['.html', '.htm', '.xhtml'],
    aliases: ['html'],
    mimeTypes: ['text/html'],
  },
  {
    id: 'css',
    name: 'CS<PERSON>',
    extensions: ['.css'],
    aliases: ['css'],
    mimeTypes: ['text/css'],
  },
  {
    id: 'scss',
    name: 'SCSS',
    extensions: ['.scss'],
    aliases: ['scss'],
    mimeTypes: ['text/x-scss'],
  },
  {
    id: 'sass',
    name: 'Sass',
    extensions: ['.sass'],
    aliases: ['sass'],
    mimeTypes: ['text/x-sass'],
  },
  {
    id: 'less',
    name: 'Less',
    extensions: ['.less'],
    aliases: ['less'],
    mimeTypes: ['text/x-less'],
  },
  {
    id: 'json',
    name: 'JSON',
    extensions: ['.json', '.jsonc'],
    aliases: ['json'],
    mimeTypes: ['application/json'],
  },
  {
    id: 'xml',
    name: 'XML',
    extensions: ['.xml', '.xsd', '.xsl', '.xslt'],
    aliases: ['xml'],
    mimeTypes: ['text/xml', 'application/xml'],
  },

  // Programming Languages
  {
    id: 'python',
    name: 'Python',
    extensions: ['.py', '.pyw', '.pyi'],
    aliases: ['python', 'py'],
    mimeTypes: ['text/x-python'],
  },
  {
    id: 'java',
    name: 'Java',
    extensions: ['.java'],
    aliases: ['java'],
    mimeTypes: ['text/x-java-source'],
  },
  {
    id: 'csharp',
    name: 'C#',
    extensions: ['.cs', '.csx'],
    aliases: ['csharp', 'c#'],
    mimeTypes: ['text/x-csharp'],
  },
  {
    id: 'cpp',
    name: 'C++',
    extensions: ['.cpp', '.cc', '.cxx', '.c++', '.hpp', '.hh', '.hxx', '.h++'],
    aliases: ['cpp', 'c++'],
    mimeTypes: ['text/x-c++src'],
  },
  {
    id: 'c',
    name: 'C',
    extensions: ['.c', '.h'],
    aliases: ['c'],
    mimeTypes: ['text/x-csrc'],
  },
  {
    id: 'rust',
    name: 'Rust',
    extensions: ['.rs'],
    aliases: ['rust', 'rs'],
    mimeTypes: ['text/rust'],
  },
  {
    id: 'go',
    name: 'Go',
    extensions: ['.go'],
    aliases: ['go', 'golang'],
    mimeTypes: ['text/x-go'],
  },
  {
    id: 'php',
    name: 'PHP',
    extensions: ['.php', '.phtml', '.php3', '.php4', '.php5', '.phps'],
    aliases: ['php'],
    mimeTypes: ['text/x-php'],
  },
  {
    id: 'ruby',
    name: 'Ruby',
    extensions: ['.rb', '.rbw'],
    aliases: ['ruby', 'rb'],
    mimeTypes: ['text/x-ruby'],
  },
  {
    id: 'swift',
    name: 'Swift',
    extensions: ['.swift'],
    aliases: ['swift'],
    mimeTypes: ['text/swift'],
  },
  {
    id: 'kotlin',
    name: 'Kotlin',
    extensions: ['.kt', '.kts'],
    aliases: ['kotlin', 'kt'],
    mimeTypes: ['text/x-kotlin'],
  },
  {
    id: 'scala',
    name: 'Scala',
    extensions: ['.scala', '.sc'],
    aliases: ['scala'],
    mimeTypes: ['text/x-scala'],
  },

  // Shell and Config
  {
    id: 'shell',
    name: 'Shell Script',
    extensions: ['.sh', '.bash', '.zsh', '.fish'],
    aliases: ['shell', 'bash', 'sh'],
    mimeTypes: ['text/x-shellscript'],
  },
  {
    id: 'powershell',
    name: 'PowerShell',
    extensions: ['.ps1', '.psm1', '.psd1'],
    aliases: ['powershell', 'ps1'],
    mimeTypes: ['text/x-powershell'],
  },
  {
    id: 'yaml',
    name: 'YAML',
    extensions: ['.yaml', '.yml'],
    aliases: ['yaml', 'yml'],
    mimeTypes: ['text/yaml'],
  },
  {
    id: 'toml',
    name: 'TOML',
    extensions: ['.toml'],
    aliases: ['toml'],
    mimeTypes: ['text/x-toml'],
  },
  {
    id: 'ini',
    name: 'INI',
    extensions: ['.ini', '.cfg', '.conf'],
    aliases: ['ini'],
    mimeTypes: ['text/x-ini'],
  },

  // Documentation
  {
    id: 'markdown',
    name: 'Markdown',
    extensions: ['.md', '.markdown', '.mdown', '.mkd'],
    aliases: ['markdown', 'md'],
    mimeTypes: ['text/markdown'],
  },
  {
    id: 'plaintext',
    name: 'Plain Text',
    extensions: ['.txt', '.text'],
    aliases: ['plaintext', 'text'],
    mimeTypes: ['text/plain'],
  },

  // Data formats
  {
    id: 'csv',
    name: 'CSV',
    extensions: ['.csv'],
    aliases: ['csv'],
    mimeTypes: ['text/csv'],
  },
  {
    id: 'sql',
    name: 'SQL',
    extensions: ['.sql'],
    aliases: ['sql'],
    mimeTypes: ['text/x-sql'],
  },

  // Framework specific
  {
    id: 'vue',
    name: 'Vue.js',
    extensions: ['.vue'],
    aliases: ['vue'],
    mimeTypes: ['text/x-vue'],
  },
  {
    id: 'svelte',
    name: 'Svelte',
    extensions: ['.svelte'],
    aliases: ['svelte'],
    mimeTypes: ['text/x-svelte'],
  },
];

/**
 * Get language ID from file extension
 */
export function getLanguageFromExtension(filePath: string): string {
  const extension = getFileExtension(filePath);
  
  for (const lang of SUPPORTED_LANGUAGES) {
    if (lang.extensions.includes(extension)) {
      return lang.id;
    }
  }
  
  return 'plaintext';
}

/**
 * Get file extension from path
 */
export function getFileExtension(filePath: string): string {
  const lastDot = filePath.lastIndexOf('.');
  if (lastDot === -1) return '';
  return filePath.substring(lastDot).toLowerCase();
}

/**
 * Get language info by ID
 */
export function getLanguageInfo(languageId: string): LanguageInfo | undefined {
  return SUPPORTED_LANGUAGES.find(lang => lang.id === languageId);
}

/**
 * Get all supported languages
 */
export function getAllLanguages(): LanguageInfo[] {
  return SUPPORTED_LANGUAGES;
}

/**
 * Check if a language is supported
 */
export function isLanguageSupported(languageId: string): boolean {
  return SUPPORTED_LANGUAGES.some(lang => lang.id === languageId);
}

/**
 * Get file icon based on language
 */
export function getFileIcon(filePath: string): string {
  const language = getLanguageFromExtension(filePath);
  
  const iconMap: Record<string, string> = {
    javascript: '📄',
    typescript: '📘',
    html: '🌐',
    css: '🎨',
    scss: '🎨',
    sass: '🎨',
    less: '🎨',
    json: '📋',
    xml: '📄',
    python: '🐍',
    java: '☕',
    csharp: '🔷',
    cpp: '⚙️',
    c: '⚙️',
    rust: '🦀',
    go: '🐹',
    php: '🐘',
    ruby: '💎',
    swift: '🦉',
    kotlin: '🎯',
    scala: '⚡',
    shell: '🐚',
    powershell: '💙',
    yaml: '📝',
    toml: '📝',
    ini: '⚙️',
    markdown: '📖',
    plaintext: '📄',
    csv: '📊',
    sql: '🗃️',
    vue: '💚',
    svelte: '🧡',
  };
  
  return iconMap[language] || '📄';
}
