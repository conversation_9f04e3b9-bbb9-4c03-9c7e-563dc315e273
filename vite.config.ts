import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  base: './',
  root: path.join(__dirname, 'src/renderer'),
  publicDir: path.join(__dirname, 'src/renderer/public'),
  build: {
    outDir: path.join(__dirname, 'build/renderer'),
    emptyOutDir: true,
  },
  server: {
    port: 3000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/renderer/src/components'),
      '@/pages': path.resolve(__dirname, 'src/renderer/src/pages'),
      '@/styles': path.resolve(__dirname, 'src/renderer/src/styles'),
      '@/assets': path.resolve(__dirname, 'src/renderer/src/assets'),
      '@/hooks': path.resolve(__dirname, 'src/renderer/src/hooks'),
      '@/store': path.resolve(__dirname, 'src/renderer/src/store'),
      '@/utils': path.resolve(__dirname, 'src/renderer/src/utils'),
      '@/main': path.resolve(__dirname, 'src/main'),
    },
  },
});
