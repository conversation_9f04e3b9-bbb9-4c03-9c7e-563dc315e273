{"compilerOptions": {"outDir": "../../dist/main", "target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "lib": ["ES2020"], "types": ["node"]}, "include": ["**/*"], "exclude": ["node_modules", "../../dist"]}