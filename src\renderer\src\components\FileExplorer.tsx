import React, { useState, useEffect, useCallback } from 'react';
import { useFileOperations } from '../hooks/useFileOperations';
import { getFileIcon } from '../utils/languageDetection';
import { ChevronRight, ChevronDown, File, Folder, Plus, FolderPlus, RefreshCw, Minus } from 'lucide-react';
import '../styles/FileExplorer.css';

interface FileExplorerProps {
  projectPath: string;
}

interface FileItem {
  name: string;
  path: string;
  isDirectory: boolean;
  isExpanded?: boolean;
  children?: FileItem[];
  size?: number;
  modified?: Date;
}

const FileExplorer: React.FC<FileExplorerProps> = ({ projectPath }) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { openFile, createNewFile } = useFileOperations();

  const loadFiles = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use the new file system API to read the directory
      const fileInfos = await window.electron.fileSystem.readDirectory(projectPath, false);

      const convertToFileItem = (fileInfo: any): FileItem => ({
        name: fileInfo.name,
        path: fileInfo.path,
        isDirectory: fileInfo.isDirectory,
        isExpanded: false,
        children: fileInfo.isDirectory ? [] : undefined,
        size: fileInfo.size,
        modified: new Date(fileInfo.modified),
      });

      const fileItems = fileInfos.map(convertToFileItem);
      setFiles(fileItems);
    } catch (err: any) {
      console.error('Failed to load files:', err);
      setError(`Failed to load project files: ${err.message || 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  }, [projectPath]);

  useEffect(() => {
    loadFiles();
  }, [loadFiles]);
  
  const toggleDirectory = async (path: string) => {
    const updateFiles = async (items: FileItem[]): Promise<FileItem[]> => {
      const updatedItems: FileItem[] = [];

      for (const item of items) {
        if (item.path === path) {
          const newExpanded = !item.isExpanded;
          let children = item.children;

          // Load children if expanding and not already loaded
          if (newExpanded && item.isDirectory && (!children || children.length === 0)) {
            try {
              const fileInfos = await window.electron.fileSystem.readDirectory(item.path, false);
              children = fileInfos.map(fileInfo => ({
                name: fileInfo.name,
                path: fileInfo.path,
                isDirectory: fileInfo.isDirectory,
                isExpanded: false,
                children: fileInfo.isDirectory ? [] : undefined
              }));
            } catch (error) {
              console.error('Failed to load directory contents:', error);
              children = [];
            }
          }

          updatedItems.push({ ...item, isExpanded: newExpanded, children });
        } else if (item.children) {
          const updatedChildren = await updateFiles(item.children);
          updatedItems.push({ ...item, children: updatedChildren });
        } else {
          updatedItems.push(item);
        }
      }

      return updatedItems;
    };

    const updatedFiles = await updateFiles(files);
    setFiles(updatedFiles);
  };
  
  const handleFileClick = async (file: FileItem) => {
    if (file.isDirectory) {
      await toggleDirectory(file.path);
    } else {
      const result = await openFile(file.path);
      if (!result.success) {
        console.error('Failed to open file:', result.error);
        // TODO: Show error notification
      }
    }
  };

  const handleNewFile = async () => {
    const result = await createNewFile();
    if (!result.success) {
      console.error('Failed to create new file:', result.error);
      // TODO: Show error notification
    }
  };

  const handleNewFolder = async () => {
    // TODO: Implement new folder creation
    console.log('New folder creation not implemented yet');
  };

  const handleRefresh = () => {
    loadFiles();
  };

  const collapseAll = () => {
    const collapseItems = (items: FileItem[]): FileItem[] => {
      return items.map(item => ({
        ...item,
        isExpanded: false,
        children: item.children ? collapseItems(item.children) : undefined,
      }));
    };
    setFiles(collapseItems(files));
  };
  
  const renderFileTree = (items: FileItem[], level = 0) => {
    return items.map(item => (
      <React.Fragment key={item.path}>
        <div
          className={`file-item ${item.isDirectory ? 'directory' : 'file'}`}
          style={{ paddingLeft: `${level * 16}px` }}
          onClick={() => handleFileClick(item)}
        >
          <div className="file-icon">
            {item.isDirectory ? (
              <>
                {item.isExpanded ? (
                  <ChevronDown size={16} className="chevron-icon" />
                ) : (
                  <ChevronRight size={16} className="chevron-icon" />
                )}
                <Folder size={16} className="folder-icon" />
              </>
            ) : (
              <span className="file-emoji">{getFileIcon(item.path)}</span>
            )}
          </div>
          <div className="file-name" title={item.path}>{item.name}</div>
          {!item.isDirectory && item.size && (
            <div className="file-size">{formatFileSize(item.size)}</div>
          )}
        </div>

        {item.isDirectory && item.isExpanded && item.children && (
          renderFileTree(item.children, level + 1)
        )}
      </React.Fragment>
    ));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };
  
  return (
    <div className="file-explorer">
      <div className="explorer-header">
        <h3>Explorer</h3>
        <div className="explorer-actions">
          <button
            className="explorer-action"
            title="New File"
            onClick={handleNewFile}
          >
            <Plus size={16} />
          </button>
          <button
            className="explorer-action"
            title="New Folder"
            onClick={handleNewFolder}
          >
            <FolderPlus size={16} />
          </button>
          <button
            className="explorer-action"
            title="Refresh"
            onClick={handleRefresh}
          >
            <RefreshCw size={16} />
          </button>
          <button
            className="explorer-action"
            title="Collapse All"
            onClick={collapseAll}
          >
            <Minus size={16} />
          </button>
        </div>
      </div>
      
      <div className="explorer-content">
        {isLoading ? (
          <div className="explorer-loading">
            <div className="spinner-ring small"></div>
            <span>Loading files...</span>
          </div>
        ) : error ? (
          <div className="explorer-error">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="12"/>
              <line x1="12" y1="16" x2="12.01" y2="16"/>
            </svg>
            <span>{error}</span>
          </div>
        ) : (
          <div className="file-tree">
            {renderFileTree(files)}
          </div>
        )}
      </div>
    </div>
  );
};

export default FileExplorer;
