import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Search, Command, FileText, Terminal, Package, Settings, Zap, ChevronRight } from 'lucide-react';
import '../styles/CommandPalette.css';

// Command interface
export interface Command {
  id: string;
  title: string;
  description?: string;
  category: string;
  icon?: React.ReactNode;
  shortcut?: string;
  action: () => void | Promise<void>;
  keywords?: string[];
}

interface CommandPaletteProps {
  isVisible: boolean;
  onClose: () => void;
  commands: Command[];
}

const CommandPalette: React.FC<CommandPaletteProps> = ({ isVisible, onClose, commands }) => {
  const [query, setQuery] = useState('');
  const [filteredCommands, setFilteredCommands] = useState<Command[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [categories, setCategories] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Filter commands based on query
  useEffect(() => {
    if (!query.trim()) {
      setFilteredCommands(commands);
    } else {
      const searchQuery = query.toLowerCase();
      const filtered = commands.filter(command => {
        const titleMatch = command.title.toLowerCase().includes(searchQuery);
        const descriptionMatch = command.description?.toLowerCase().includes(searchQuery);
        const categoryMatch = command.category.toLowerCase().includes(searchQuery);
        const keywordMatch = command.keywords?.some(keyword => 
          keyword.toLowerCase().includes(searchQuery)
        );
        
        return titleMatch || descriptionMatch || categoryMatch || keywordMatch;
      });
      
      setFilteredCommands(filtered);
    }
    setSelectedIndex(0);
  }, [query, commands]);

  // Get unique categories
  useEffect(() => {
    const uniqueCategories = Array.from(new Set(commands.map(cmd => cmd.category)));
    setCategories(uniqueCategories);
  }, [commands]);

  // Focus input when visible
  useEffect(() => {
    if (isVisible && inputRef.current) {
      inputRef.current.focus();
      setQuery('');
      setSelectedIndex(0);
    }
  }, [isVisible]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isVisible) return;

    switch (e.key) {
      case 'Escape':
        e.preventDefault();
        onClose();
        break;
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredCommands.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : filteredCommands.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (filteredCommands[selectedIndex]) {
          executeCommand(filteredCommands[selectedIndex]);
        }
        break;
    }
  }, [isVisible, filteredCommands, selectedIndex, onClose]);

  // Add keyboard event listener
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Scroll selected item into view
  useEffect(() => {
    if (listRef.current) {
      const selectedElement = listRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        });
      }
    }
  }, [selectedIndex]);

  /**
   * Execute a command
   */
  const executeCommand = async (command: Command) => {
    try {
      await command.action();
      onClose();
    } catch (error) {
      console.error('Failed to execute command:', error);
      // TODO: Show error notification
    }
  };

  /**
   * Get command icon
   */
  const getCommandIcon = (command: Command) => {
    if (command.icon) {
      return command.icon;
    }

    // Default icons based on category
    switch (command.category.toLowerCase()) {
      case 'file':
        return <FileText size={16} />;
      case 'terminal':
        return <Terminal size={16} />;
      case 'extension':
        return <Package size={16} />;
      case 'settings':
        return <Settings size={16} />;
      default:
        return <Zap size={16} />;
    }
  };

  /**
   * Get category color
   */
  const getCategoryColor = (category: string) => {
    const colors = [
      'var(--neon-cyan)',
      'var(--success)',
      'var(--warning)',
      'var(--info)',
      'var(--error)',
      'var(--neon-blue)',
    ];
    
    const index = categories.indexOf(category) % colors.length;
    return colors[index];
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="command-palette-overlay" onClick={onClose}>
      <div className="command-palette" onClick={(e) => e.stopPropagation()}>
        <div className="command-palette-header">
          <div className="command-palette-search">
            <Search size={20} />
            <input
              ref={inputRef}
              type="text"
              placeholder="Type a command or search..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="command-palette-input"
            />
            <div className="command-palette-shortcut">
              <Command size={14} />
              <span>⇧P</span>
            </div>
          </div>
        </div>

        <div className="command-palette-content">
          {filteredCommands.length === 0 ? (
            <div className="command-palette-empty">
              <Search size={48} />
              <h3>No commands found</h3>
              <p>Try a different search term or browse available commands.</p>
            </div>
          ) : (
            <div className="command-palette-list" ref={listRef}>
              {filteredCommands.map((command, index) => (
                <div
                  key={command.id}
                  className={`command-palette-item ${index === selectedIndex ? 'selected' : ''}`}
                  onClick={() => executeCommand(command)}
                  onMouseEnter={() => setSelectedIndex(index)}
                >
                  <div className="command-item-icon">
                    {getCommandIcon(command)}
                  </div>
                  
                  <div className="command-item-content">
                    <div className="command-item-header">
                      <span className="command-item-title">{command.title}</span>
                      {command.shortcut && (
                        <span className="command-item-shortcut">{command.shortcut}</span>
                      )}
                    </div>
                    {command.description && (
                      <span className="command-item-description">{command.description}</span>
                    )}
                  </div>

                  <div className="command-item-meta">
                    <span 
                      className="command-item-category"
                      style={{ color: getCategoryColor(command.category) }}
                    >
                      {command.category}
                    </span>
                    <ChevronRight size={14} className="command-item-arrow" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="command-palette-footer">
          <div className="command-palette-tips">
            <span className="command-tip">
              <kbd>↑</kbd><kbd>↓</kbd> to navigate
            </span>
            <span className="command-tip">
              <kbd>Enter</kbd> to execute
            </span>
            <span className="command-tip">
              <kbd>Esc</kbd> to close
            </span>
          </div>
          <div className="command-palette-count">
            {filteredCommands.length} command{filteredCommands.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommandPalette;
