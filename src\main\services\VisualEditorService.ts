import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';
import { BrowserWindow } from 'electron';
import { DatabaseManager } from './DatabaseManager';
import { FileSystemService } from './FileSystemService';

// Visual element types
export interface VisualElement {
  id: string;
  type: 'div' | 'span' | 'button' | 'input' | 'img' | 'text' | 'component';
  tagName: string;
  className?: string;
  styles: Record<string, string>;
  attributes: Record<string, string>;
  content?: string;
  children: VisualElement[];
  parent?: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  metadata: {
    filePath: string;
    lineNumber: number;
    columnNumber: number;
    framework?: 'react' | 'vue' | 'svelte' | 'html';
  };
}

// Visual editing modes
export enum EditingMode {
  SELECT = 'select',
  EDIT = 'edit',
  INSERT = 'insert',
  RESIZE = 'resize',
  MOVE = 'move'
}

// Visual editing project
export interface VisualProject {
  id: string;
  name: string;
  path: string;
  framework: 'react' | 'vue' | 'svelte' | 'html';
  entryPoint: string;
  buildCommand?: string;
  devCommand?: string;
  outputDir?: string;
  publicDir?: string;
  components: VisualElement[];
  pages: VisualPage[];
  assets: VisualAsset[];
  createdAt: Date;
  updatedAt: Date;
}

// Visual page
export interface VisualPage {
  id: string;
  name: string;
  path: string;
  route: string;
  filePath: string;
  elements: VisualElement[];
  metadata: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
}

// Visual asset
export interface VisualAsset {
  id: string;
  name: string;
  type: 'image' | 'icon' | 'font' | 'video' | 'audio';
  path: string;
  url: string;
  size: number;
  dimensions?: {
    width: number;
    height: number;
  };
}

// Live preview configuration
export interface LivePreviewConfig {
  port: number;
  host: string;
  autoReload: boolean;
  hotReload: boolean;
  openBrowser: boolean;
  proxy?: Record<string, string>;
}

/**
 * Visual Editor Service
 * Provides Onlook-style visual editing capabilities
 */
export class VisualEditorService extends EventEmitter {
  private db: DatabaseManager;
  private fs: FileSystemService;
  private projects: Map<string, VisualProject> = new Map();
  private activeProject: VisualProject | null = null;
  private previewWindow: BrowserWindow | null = null;
  private devServer: any = null;
  private isInitialized = false;

  constructor(db: DatabaseManager, fs: FileSystemService) {
    super();
    this.db = db;
    this.fs = fs;
  }

  /**
   * Initialize Visual Editor Service
   */
  public async initialize(): Promise<void> {
    try {
      // Load visual projects from database
      await this.loadProjects();
      
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize Visual Editor:', error instanceof Error ? error.message : String(error));
      this.emit('error', error);
    }
  }

  /**
   * Load visual projects from database
   */
  private async loadProjects(): Promise<void> {
    try {
      const projects = await this.db.getAll('visual_projects');
      for (const project of projects) {
        this.projects.set(project.id, project);
      }
    } catch (error) {
      console.warn('Failed to load visual projects:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Create a new visual project
   */
  public async createProject(
    name: string,
    projectPath: string,
    framework: 'react' | 'vue' | 'svelte' | 'html'
  ): Promise<VisualProject> {
    try {
      const project: VisualProject = {
        id: this.generateProjectId(),
        name,
        path: projectPath,
        framework,
        entryPoint: this.getDefaultEntryPoint(framework),
        components: [],
        pages: [],
        assets: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Analyze project structure
      await this.analyzeProject(project);

      // Save project to database
      await this.db.set('visual_projects', project.id, project);
      
      // Add to projects map
      this.projects.set(project.id, project);
      
      this.emit('project-created', project);
      return project;
    } catch (error) {
      console.error('Failed to create visual project:', error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * Open a visual project
   */
  public async openProject(projectId: string): Promise<VisualProject | null> {
    try {
      const project = this.projects.get(projectId);
      if (!project) {
        throw new Error(`Visual project ${projectId} not found`);
      }

      // Re-analyze project to get latest structure
      await this.analyzeProject(project);
      
      this.activeProject = project;
      this.emit('project-opened', project);
      
      return project;
    } catch (error) {
      console.error('Failed to open visual project:', error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * Analyze project structure
   */
  private async analyzeProject(project: VisualProject): Promise<void> {
    try {
      // Reset project data
      project.components = [];
      project.pages = [];
      project.assets = [];

      // Analyze based on framework
      switch (project.framework) {
        case 'react':
          await this.analyzeReactProject(project);
          break;
        case 'vue':
          await this.analyzeVueProject(project);
          break;
        case 'svelte':
          await this.analyzeSvelteProject(project);
          break;
        case 'html':
          await this.analyzeHtmlProject(project);
          break;
      }

      // Find assets
      await this.findAssets(project);

      // Update project
      project.updatedAt = new Date();
      await this.db.set('visual_projects', project.id, project);
    } catch (error) {
      console.error('Failed to analyze project:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Analyze React project
   */
  private async analyzeReactProject(project: VisualProject): Promise<void> {
    const srcPath = path.join(project.path, 'src');
    
    if (!fs.existsSync(srcPath)) {
      return;
    }

    // Find React components
    await this.findReactComponents(project, srcPath);
    
    // Find pages/routes
    await this.findReactPages(project, srcPath);
  }

  /**
   * Find React components
   */
  private async findReactComponents(project: VisualProject, srcPath: string): Promise<void> {
    const componentPatterns = [
      '**/*.jsx',
      '**/*.tsx',
      '**/components/**/*.js',
      '**/components/**/*.ts'
    ];

    for (const pattern of componentPatterns) {
      const files = await this.fs.glob(pattern, { cwd: srcPath });
      
      for (const file of files) {
        const filePath = path.join(srcPath, file);
        await this.parseReactComponent(project, filePath);
      }
    }
  }

  /**
   * Parse React component
   */
  private async parseReactComponent(project: VisualProject, filePath: string): Promise<void> {
    try {
      const content = await fs.promises.readFile(filePath, 'utf-8');
      
      // Simple JSX parsing (in a real implementation, use a proper parser like @babel/parser)
      const jsxElements = this.extractJSXElements(content, filePath);
      
      for (const element of jsxElements) {
        project.components.push(element);
      }
    } catch (error) {
      console.warn(`Failed to parse React component ${filePath}:`, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * Extract JSX elements (simplified)
   */
  private extractJSXElements(content: string, filePath: string): VisualElement[] {
    const elements: VisualElement[] = [];
    
    // This is a simplified implementation
    // In a real implementation, use a proper AST parser
    const jsxRegex = /<(\w+)([^>]*?)(?:\/>|>(.*?)<\/\1>)/gs;
    let match;
    let lineNumber = 1;

    while ((match = jsxRegex.exec(content)) !== null) {
      const [fullMatch, tagName, attributes, children] = match;
      
      // Count line numbers
      const beforeMatch = content.substring(0, match.index);
      lineNumber = (beforeMatch.match(/\n/g) || []).length + 1;

      const element: VisualElement = {
        id: this.generateElementId(),
        type: this.mapTagToType(tagName),
        tagName,
        className: this.extractClassName(attributes),
        styles: this.extractStyles(attributes),
        attributes: this.extractAttributes(attributes),
        content: children?.trim(),
        children: [],
        position: { x: 0, y: 0, width: 0, height: 0 },
        metadata: {
          filePath,
          lineNumber,
          columnNumber: match.index - beforeMatch.lastIndexOf('\n'),
          framework: 'react'
        }
      };

      elements.push(element);
    }

    return elements;
  }

  /**
   * Find React pages
   */
  private async findReactPages(project: VisualProject, srcPath: string): Promise<void> {
    const pagePatterns = [
      '**/pages/**/*.jsx',
      '**/pages/**/*.tsx',
      '**/views/**/*.jsx',
      '**/views/**/*.tsx',
      'App.jsx',
      'App.tsx'
    ];

    for (const pattern of pagePatterns) {
      const files = await this.fs.glob(pattern, { cwd: srcPath });
      
      for (const file of files) {
        const filePath = path.join(srcPath, file);
        const page = await this.createPageFromFile(filePath, 'react');
        if (page) {
          project.pages.push(page);
        }
      }
    }
  }

  /**
   * Analyze Vue project
   */
  private async analyzeVueProject(project: VisualProject): Promise<void> {
    // Similar implementation for Vue
    console.log('Vue project analysis not yet implemented');
  }

  /**
   * Analyze Svelte project
   */
  private async analyzeSvelteProject(project: VisualProject): Promise<void> {
    // Similar implementation for Svelte
    console.log('Svelte project analysis not yet implemented');
  }

  /**
   * Analyze HTML project
   */
  private async analyzeHtmlProject(project: VisualProject): Promise<void> {
    const htmlFiles = await this.fs.glob('**/*.html', { cwd: project.path });
    
    for (const file of htmlFiles) {
      const filePath = path.join(project.path, file);
      const page = await this.createPageFromFile(filePath, 'html');
      if (page) {
        project.pages.push(page);
      }
    }
  }

  /**
   * Create page from file
   */
  private async createPageFromFile(filePath: string, framework: string): Promise<VisualPage | null> {
    try {
      const content = await fs.promises.readFile(filePath, 'utf-8');
      const fileName = path.basename(filePath, path.extname(filePath));
      
      const page: VisualPage = {
        id: this.generatePageId(),
        name: fileName,
        path: filePath,
        route: `/${fileName}`,
        filePath,
        elements: [],
        metadata: {
          title: this.extractTitle(content),
          description: this.extractDescription(content)
        }
      };

      // Parse elements based on framework
      if (framework === 'html') {
        page.elements = this.extractHTMLElements(content, filePath);
      } else {
        page.elements = this.extractJSXElements(content, filePath);
      }

      return page;
    } catch (error) {
      console.warn(`Failed to create page from ${filePath}:`, error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * Extract HTML elements
   */
  private extractHTMLElements(content: string, filePath: string): VisualElement[] {
    const elements: VisualElement[] = [];
    
    // Simple HTML parsing
    const htmlRegex = /<(\w+)([^>]*?)(?:\/>|>(.*?)<\/\1>)/gs;
    let match;
    let lineNumber = 1;

    while ((match = htmlRegex.exec(content)) !== null) {
      const [fullMatch, tagName, attributes, children] = match;
      
      // Skip script and style tags
      if (['script', 'style', 'meta', 'link', 'title'].includes(tagName.toLowerCase())) {
        continue;
      }

      const beforeMatch = content.substring(0, match.index);
      lineNumber = (beforeMatch.match(/\n/g) || []).length + 1;

      const element: VisualElement = {
        id: this.generateElementId(),
        type: this.mapTagToType(tagName),
        tagName,
        className: this.extractClassName(attributes),
        styles: this.extractStyles(attributes),
        attributes: this.extractAttributes(attributes),
        content: children?.trim(),
        children: [],
        position: { x: 0, y: 0, width: 0, height: 0 },
        metadata: {
          filePath,
          lineNumber,
          columnNumber: match.index - beforeMatch.lastIndexOf('\n'),
          framework: 'html'
        }
      };

      elements.push(element);
    }

    return elements;
  }

  /**
   * Find assets in project
   */
  private async findAssets(project: VisualProject): Promise<void> {
    const assetPatterns = [
      '**/*.png',
      '**/*.jpg',
      '**/*.jpeg',
      '**/*.gif',
      '**/*.svg',
      '**/*.ico',
      '**/*.woff',
      '**/*.woff2',
      '**/*.ttf',
      '**/*.otf',
      '**/*.mp4',
      '**/*.webm',
      '**/*.mp3',
      '**/*.wav'
    ];

    for (const pattern of assetPatterns) {
      const files = await this.fs.glob(pattern, { cwd: project.path });
      
      for (const file of files) {
        const filePath = path.join(project.path, file);
        const asset = await this.createAssetFromFile(filePath);
        if (asset) {
          project.assets.push(asset);
        }
      }
    }
  }

  /**
   * Create asset from file
   */
  private async createAssetFromFile(filePath: string): Promise<VisualAsset | null> {
    try {
      const stats = await fs.promises.stat(filePath);
      const ext = path.extname(filePath).toLowerCase();
      const fileName = path.basename(filePath);
      
      const asset: VisualAsset = {
        id: this.generateAssetId(),
        name: fileName,
        type: this.getAssetType(ext),
        path: filePath,
        url: filePath, // This would be converted to a URL in a real implementation
        size: stats.size
      };

      // Get image dimensions if it's an image
      if (asset.type === 'image') {
        asset.dimensions = await this.getImageDimensions(filePath);
      }

      return asset;
    } catch (error) {
      console.warn(`Failed to create asset from ${filePath}:`, error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * Get asset type from extension
   */
  private getAssetType(ext: string): VisualAsset['type'] {
    const imageExts = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'];
    const fontExts = ['.woff', '.woff2', '.ttf', '.otf'];
    const videoExts = ['.mp4', '.webm'];
    const audioExts = ['.mp3', '.wav'];

    if (imageExts.includes(ext)) return 'image';
    if (fontExts.includes(ext)) return 'font';
    if (videoExts.includes(ext)) return 'video';
    if (audioExts.includes(ext)) return 'audio';
    
    return 'image'; // default
  }

  /**
   * Get image dimensions (placeholder)
   */
  private async getImageDimensions(filePath: string): Promise<{ width: number; height: number } | undefined> {
    // In a real implementation, use a library like sharp or image-size
    return { width: 0, height: 0 };
  }

  /**
   * Helper methods for parsing
   */
  private mapTagToType(tagName: string): VisualElement['type'] {
    const tagMap: Record<string, VisualElement['type']> = {
      'div': 'div',
      'span': 'span',
      'button': 'button',
      'input': 'input',
      'img': 'img',
      'p': 'text',
      'h1': 'text',
      'h2': 'text',
      'h3': 'text',
      'h4': 'text',
      'h5': 'text',
      'h6': 'text'
    };

    return tagMap[tagName.toLowerCase()] || 'div';
  }

  private extractClassName(attributes: string): string | undefined {
    const classMatch = attributes.match(/className=["']([^"']+)["']/);
    return classMatch ? classMatch[1] : undefined;
  }

  private extractStyles(attributes: string): Record<string, string> {
    const styleMatch = attributes.match(/style=["']([^"']+)["']/);
    if (!styleMatch) return {};

    const styles: Record<string, string> = {};
    const styleString = styleMatch[1];
    const declarations = styleString.split(';');

    for (const declaration of declarations) {
      const [property, value] = declaration.split(':').map(s => s.trim());
      if (property && value) {
        styles[property] = value;
      }
    }

    return styles;
  }

  private extractAttributes(attributes: string): Record<string, string> {
    const attrs: Record<string, string> = {};
    const attrRegex = /(\w+)=["']([^"']+)["']/g;
    let match;

    while ((match = attrRegex.exec(attributes)) !== null) {
      const [, name, value] = match;
      if (name !== 'className' && name !== 'style') {
        attrs[name] = value;
      }
    }

    return attrs;
  }

  private extractTitle(content: string): string | undefined {
    const titleMatch = content.match(/<title>([^<]+)<\/title>/i);
    return titleMatch ? titleMatch[1] : undefined;
  }

  private extractDescription(content: string): string | undefined {
    const descMatch = content.match(/<meta\s+name=["']description["']\s+content=["']([^"']+)["']/i);
    return descMatch ? descMatch[1] : undefined;
  }

  private getDefaultEntryPoint(framework: string): string {
    const entryPoints: Record<string, string> = {
      'react': 'src/index.js',
      'vue': 'src/main.js',
      'svelte': 'src/main.js',
      'html': 'index.html'
    };

    return entryPoints[framework] || 'index.html';
  }

  /**
   * ID generators
   */
  private generateProjectId(): string {
    return `vproj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateElementId(): string {
    return `elem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generatePageId(): string {
    return `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAssetId(): string {
    return `asset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get all visual projects
   */
  public getAllProjects(): VisualProject[] {
    return Array.from(this.projects.values());
  }

  /**
   * Get active project
   */
  public getActiveProject(): VisualProject | null {
    return this.activeProject;
  }

  /**
   * Delete visual project
   */
  public async deleteProject(projectId: string): Promise<boolean> {
    try {
      this.projects.delete(projectId);
      await this.db.delete('visual_projects', projectId);
      
      if (this.activeProject?.id === projectId) {
        this.activeProject = null;
      }
      
      this.emit('project-deleted', projectId);
      return true;
    } catch (error) {
      console.error('Failed to delete visual project:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Dispose service
   */
  public dispose(): void {
    this.projects.clear();
    this.activeProject = null;
    
    if (this.previewWindow) {
      this.previewWindow.close();
      this.previewWindow = null;
    }
    
    if (this.devServer) {
      this.devServer.close();
      this.devServer = null;
    }
    
    this.removeAllListeners();
  }
}
