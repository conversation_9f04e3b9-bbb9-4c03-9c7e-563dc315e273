import { EventEmitter } from 'events';
import { spawn, exec } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { DatabaseManager } from './DatabaseManager';

const execAsync = promisify(exec);

// Git status types
export enum GitFileStatus {
  UNTRACKED = 'untracked',
  MODIFIED = 'modified',
  ADDED = 'added',
  DELETED = 'deleted',
  RENAMED = 'renamed',
  COPIED = 'copied',
  UNMERGED = 'unmerged'
}

// Git file interface
export interface GitFile {
  path: string;
  status: GitFileStatus;
  staged: boolean;
  workingTree: boolean;
  oldPath?: string; // For renamed files
}

// Git commit interface
export interface GitCommit {
  hash: string;
  shortHash: string;
  author: string;
  email: string;
  date: Date;
  message: string;
  parents: string[];
  refs: string[];
}

// Git branch interface
export interface GitBranch {
  name: string;
  current: boolean;
  remote: boolean;
  upstream?: string;
  ahead?: number;
  behind?: number;
}

// Git remote interface
export interface GitRemote {
  name: string;
  url: string;
  type: 'fetch' | 'push';
}

// Git repository status
export interface GitStatus {
  branch: string;
  ahead: number;
  behind: number;
  files: GitFile[];
  clean: boolean;
  hasStaged: boolean;
  hasUnstaged: boolean;
  hasUntracked: boolean;
}

// Git diff interface
export interface GitDiff {
  file: string;
  oldFile?: string;
  hunks: GitDiffHunk[];
  binary: boolean;
  newFile: boolean;
  deletedFile: boolean;
  renamedFile: boolean;
}

// Git diff hunk
export interface GitDiffHunk {
  oldStart: number;
  oldLines: number;
  newStart: number;
  newLines: number;
  header: string;
  lines: GitDiffLine[];
}

// Git diff line
export interface GitDiffLine {
  type: 'context' | 'addition' | 'deletion';
  content: string;
  oldLineNumber?: number;
  newLineNumber?: number;
}

// Git configuration
export interface GitConfig {
  user: {
    name?: string;
    email?: string;
  };
  core: {
    editor?: string;
    autocrlf?: boolean;
  };
  remote: Record<string, GitRemote>;
}

/**
 * Git Service
 * Provides Git integration with visual tools
 */
export class GitService extends EventEmitter {
  private db: DatabaseManager;
  private repositories: Map<string, string> = new Map(); // path -> repo root
  private isInitialized = false;

  constructor(db: DatabaseManager) {
    super();
    this.db = db;
  }

  /**
   * Initialize Git Service
   */
  public async initialize(): Promise<void> {
    try {
      // Check if git is available
      await this.checkGitAvailability();
      
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize Git service:', error instanceof Error ? error.message : String(error));
      this.emit('error', error);
    }
  }

  /**
   * Check if git is available
   */
  private async checkGitAvailability(): Promise<void> {
    try {
      await execAsync('git --version');
    } catch (error) {
      throw new Error('Git is not installed or not available in PATH');
    }
  }

  /**
   * Find git repository root
   */
  public async findRepositoryRoot(workingDir: string): Promise<string | null> {
    try {
      // Check cache first
      if (this.repositories.has(workingDir)) {
        return this.repositories.get(workingDir)!;
      }

      const { stdout } = await execAsync('git rev-parse --show-toplevel', { cwd: workingDir });
      const repoRoot = stdout.trim();
      
      // Cache the result
      this.repositories.set(workingDir, repoRoot);
      
      return repoRoot;
    } catch (error) {
      return null;
    }
  }

  /**
   * Initialize a new git repository
   */
  public async initRepository(workingDir: string): Promise<boolean> {
    try {
      await execAsync('git init', { cwd: workingDir });
      
      // Cache the new repository
      this.repositories.set(workingDir, workingDir);
      
      this.emit('repository-initialized', workingDir);
      return true;
    } catch (error) {
      console.error('Failed to initialize git repository:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Get repository status
   */
  public async getStatus(workingDir: string): Promise<GitStatus | null> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return null;

      // Get current branch and tracking info
      const branchInfo = await this.getCurrentBranch(repoRoot);
      
      // Get file status
      const { stdout } = await execAsync('git status --porcelain=v1 -b', { cwd: repoRoot });
      const lines = stdout.split('\n').filter(line => line.trim());
      
      const files: GitFile[] = [];
      let ahead = 0;
      let behind = 0;
      let branch = 'main';

      for (const line of lines) {
        if (line.startsWith('##')) {
          // Branch information
          const branchMatch = line.match(/## ([^.]+)(?:\.\.\.([^[\s]+))?(?:\s+\[([^\]]+)\])?/);
          if (branchMatch) {
            branch = branchMatch[1];
            
            // Parse ahead/behind info
            const trackingInfo = branchMatch[3];
            if (trackingInfo) {
              const aheadMatch = trackingInfo.match(/ahead (\d+)/);
              const behindMatch = trackingInfo.match(/behind (\d+)/);
              
              if (aheadMatch) ahead = parseInt(aheadMatch[1]);
              if (behindMatch) behind = parseInt(behindMatch[1]);
            }
          }
        } else {
          // File status
          const statusCode = line.substring(0, 2);
          const filePath = line.substring(3);
          
          const file = this.parseFileStatus(statusCode, filePath);
          if (file) {
            files.push(file);
          }
        }
      }

      const status: GitStatus = {
        branch,
        ahead,
        behind,
        files,
        clean: files.length === 0,
        hasStaged: files.some(f => f.staged),
        hasUnstaged: files.some(f => f.workingTree),
        hasUntracked: files.some(f => f.status === GitFileStatus.UNTRACKED)
      };

      return status;
    } catch (error) {
      console.error('Failed to get git status:', error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * Parse file status from git status output
   */
  private parseFileStatus(statusCode: string, filePath: string): GitFile | null {
    const indexStatus = statusCode[0];
    const workingTreeStatus = statusCode[1];
    
    let status: GitFileStatus;
    let staged = false;
    let workingTree = false;
    let oldPath: string | undefined;

    // Handle renamed files
    if (filePath.includes(' -> ')) {
      const [old, newPath] = filePath.split(' -> ');
      oldPath = old;
      filePath = newPath;
      status = GitFileStatus.RENAMED;
    }

    // Determine status
    if (indexStatus === '?' || workingTreeStatus === '?') {
      status = GitFileStatus.UNTRACKED;
      workingTree = true;
    } else if (indexStatus === 'A' || workingTreeStatus === 'A') {
      status = GitFileStatus.ADDED;
      staged = indexStatus === 'A';
      workingTree = workingTreeStatus === 'A';
    } else if (indexStatus === 'M' || workingTreeStatus === 'M') {
      status = GitFileStatus.MODIFIED;
      staged = indexStatus === 'M';
      workingTree = workingTreeStatus === 'M';
    } else if (indexStatus === 'D' || workingTreeStatus === 'D') {
      status = GitFileStatus.DELETED;
      staged = indexStatus === 'D';
      workingTree = workingTreeStatus === 'D';
    } else if (indexStatus === 'R' || workingTreeStatus === 'R') {
      status = GitFileStatus.RENAMED;
      staged = indexStatus === 'R';
      workingTree = workingTreeStatus === 'R';
    } else if (indexStatus === 'C' || workingTreeStatus === 'C') {
      status = GitFileStatus.COPIED;
      staged = indexStatus === 'C';
      workingTree = workingTreeStatus === 'C';
    } else if (indexStatus === 'U' || workingTreeStatus === 'U') {
      status = GitFileStatus.UNMERGED;
      workingTree = true;
    } else {
      return null;
    }

    return {
      path: filePath,
      status,
      staged,
      workingTree,
      oldPath
    };
  }

  /**
   * Get current branch
   */
  public async getCurrentBranch(workingDir: string): Promise<string | null> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return null;

      const { stdout } = await execAsync('git branch --show-current', { cwd: repoRoot });
      return stdout.trim() || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get all branches
   */
  public async getBranches(workingDir: string): Promise<GitBranch[]> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return [];

      const { stdout } = await execAsync('git branch -vv', { cwd: repoRoot });
      const lines = stdout.split('\n').filter(line => line.trim());
      
      const branches: GitBranch[] = [];
      
      for (const line of lines) {
        const match = line.match(/^(\*?)\s+([^\s]+)\s+([a-f0-9]+)\s*(?:\[([^\]]+)\])?\s*(.*)/);
        if (match) {
          const [, current, name, hash, tracking, message] = match;
          
          let upstream: string | undefined;
          let ahead: number | undefined;
          let behind: number | undefined;
          
          if (tracking) {
            const trackingMatch = tracking.match(/([^:]+)(?::\s*(?:ahead (\d+))?(?:,\s*behind (\d+))?)?/);
            if (trackingMatch) {
              upstream = trackingMatch[1];
              if (trackingMatch[2]) ahead = parseInt(trackingMatch[2]);
              if (trackingMatch[3]) behind = parseInt(trackingMatch[3]);
            }
          }
          
          branches.push({
            name,
            current: current === '*',
            remote: false,
            upstream,
            ahead,
            behind
          });
        }
      }

      return branches;
    } catch (error) {
      console.error('Failed to get branches:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Create new branch
   */
  public async createBranch(workingDir: string, branchName: string, checkout: boolean = true): Promise<boolean> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return false;

      const command = checkout ? `git checkout -b ${branchName}` : `git branch ${branchName}`;
      await execAsync(command, { cwd: repoRoot });
      
      this.emit('branch-created', branchName);
      return true;
    } catch (error) {
      console.error('Failed to create branch:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Switch branch
   */
  public async switchBranch(workingDir: string, branchName: string): Promise<boolean> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return false;

      await execAsync(`git checkout ${branchName}`, { cwd: repoRoot });
      
      this.emit('branch-switched', branchName);
      return true;
    } catch (error) {
      console.error('Failed to switch branch:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Stage files
   */
  public async stageFiles(workingDir: string, files: string[]): Promise<boolean> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return false;

      const fileArgs = files.map(f => `"${f}"`).join(' ');
      await execAsync(`git add ${fileArgs}`, { cwd: repoRoot });
      
      this.emit('files-staged', files);
      return true;
    } catch (error) {
      console.error('Failed to stage files:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Unstage files
   */
  public async unstageFiles(workingDir: string, files: string[]): Promise<boolean> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return false;

      const fileArgs = files.map(f => `"${f}"`).join(' ');
      await execAsync(`git reset HEAD ${fileArgs}`, { cwd: repoRoot });
      
      this.emit('files-unstaged', files);
      return true;
    } catch (error) {
      console.error('Failed to unstage files:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Commit changes
   */
  public async commit(workingDir: string, message: string, amend: boolean = false): Promise<boolean> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return false;

      const amendFlag = amend ? '--amend' : '';
      await execAsync(`git commit ${amendFlag} -m "${message}"`, { cwd: repoRoot });
      
      this.emit('committed', message);
      return true;
    } catch (error) {
      console.error('Failed to commit:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Get commit history
   */
  public async getCommitHistory(workingDir: string, limit: number = 50): Promise<GitCommit[]> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return [];

      const format = '--pretty=format:%H|%h|%an|%ae|%ad|%s|%P|%D';
      const { stdout } = await execAsync(`git log ${format} --date=iso -${limit}`, { cwd: repoRoot });
      
      const lines = stdout.split('\n').filter(line => line.trim());
      const commits: GitCommit[] = [];
      
      for (const line of lines) {
        const [hash, shortHash, author, email, date, message, parents, refs] = line.split('|');
        
        commits.push({
          hash,
          shortHash,
          author,
          email,
          date: new Date(date),
          message,
          parents: parents ? parents.split(' ') : [],
          refs: refs ? refs.split(', ').filter(r => r.trim()) : []
        });
      }
      
      return commits;
    } catch (error) {
      console.error('Failed to get commit history:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Get file diff
   */
  public async getDiff(workingDir: string, filePath?: string, staged: boolean = false): Promise<GitDiff[]> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return [];

      const stagedFlag = staged ? '--cached' : '';
      const fileArg = filePath ? `"${filePath}"` : '';
      const { stdout } = await execAsync(`git diff ${stagedFlag} ${fileArg}`, { cwd: repoRoot });
      
      return this.parseDiff(stdout);
    } catch (error) {
      console.error('Failed to get diff:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Parse git diff output
   */
  private parseDiff(diffOutput: string): GitDiff[] {
    const diffs: GitDiff[] = [];
    const lines = diffOutput.split('\n');
    
    let currentDiff: GitDiff | null = null;
    let currentHunk: GitDiffHunk | null = null;
    let oldLineNumber = 0;
    let newLineNumber = 0;
    
    for (const line of lines) {
      if (line.startsWith('diff --git')) {
        // New file diff
        if (currentDiff) {
          diffs.push(currentDiff);
        }
        
        const match = line.match(/diff --git a\/(.+) b\/(.+)/);
        if (match) {
          currentDiff = {
            file: match[2],
            oldFile: match[1] !== match[2] ? match[1] : undefined,
            hunks: [],
            binary: false,
            newFile: false,
            deletedFile: false,
            renamedFile: match[1] !== match[2]
          };
        }
      } else if (line.startsWith('@@') && currentDiff) {
        // New hunk
        if (currentHunk) {
          currentDiff.hunks.push(currentHunk);
        }
        
        const match = line.match(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@(.*)/);
        if (match) {
          oldLineNumber = parseInt(match[1]);
          newLineNumber = parseInt(match[3]);
          
          currentHunk = {
            oldStart: oldLineNumber,
            oldLines: parseInt(match[2] || '1'),
            newStart: newLineNumber,
            newLines: parseInt(match[4] || '1'),
            header: match[5].trim(),
            lines: []
          };
        }
      } else if (currentHunk && (line.startsWith(' ') || line.startsWith('+') || line.startsWith('-'))) {
        // Diff line
        const type = line[0] === '+' ? 'addition' : line[0] === '-' ? 'deletion' : 'context';
        const content = line.substring(1);
        
        const diffLine: GitDiffLine = {
          type,
          content,
          oldLineNumber: type !== 'addition' ? oldLineNumber : undefined,
          newLineNumber: type !== 'deletion' ? newLineNumber : undefined
        };
        
        currentHunk.lines.push(diffLine);
        
        if (type !== 'addition') oldLineNumber++;
        if (type !== 'deletion') newLineNumber++;
      } else if (line.startsWith('new file mode') && currentDiff) {
        currentDiff.newFile = true;
      } else if (line.startsWith('deleted file mode') && currentDiff) {
        currentDiff.deletedFile = true;
      } else if (line.includes('Binary files') && currentDiff) {
        currentDiff.binary = true;
      }
    }
    
    // Add last diff and hunk
    if (currentHunk && currentDiff) {
      currentDiff.hunks.push(currentHunk);
    }
    if (currentDiff) {
      diffs.push(currentDiff);
    }
    
    return diffs;
  }

  /**
   * Push to remote
   */
  public async push(workingDir: string, remote: string = 'origin', branch?: string): Promise<boolean> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return false;

      const branchArg = branch ? branch : '';
      await execAsync(`git push ${remote} ${branchArg}`, { cwd: repoRoot });
      
      this.emit('pushed', remote, branch);
      return true;
    } catch (error) {
      console.error('Failed to push:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Pull from remote
   */
  public async pull(workingDir: string, remote: string = 'origin', branch?: string): Promise<boolean> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return false;

      const branchArg = branch ? branch : '';
      await execAsync(`git pull ${remote} ${branchArg}`, { cwd: repoRoot });
      
      this.emit('pulled', remote, branch);
      return true;
    } catch (error) {
      console.error('Failed to pull:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Get remotes
   */
  public async getRemotes(workingDir: string): Promise<GitRemote[]> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return [];

      const { stdout } = await execAsync('git remote -v', { cwd: repoRoot });
      const lines = stdout.split('\n').filter(line => line.trim());
      
      const remotes: GitRemote[] = [];
      
      for (const line of lines) {
        const match = line.match(/^(\S+)\s+(\S+)\s+\((\w+)\)$/);
        if (match) {
          const [, name, url, type] = match;
          remotes.push({
            name,
            url,
            type: type as 'fetch' | 'push'
          });
        }
      }
      
      return remotes;
    } catch (error) {
      console.error('Failed to get remotes:', error instanceof Error ? error.message : String(error));
      return [];
    }
  }

  /**
   * Add remote
   */
  public async addRemote(workingDir: string, name: string, url: string): Promise<boolean> {
    try {
      const repoRoot = await this.findRepositoryRoot(workingDir);
      if (!repoRoot) return false;

      await execAsync(`git remote add ${name} ${url}`, { cwd: repoRoot });
      
      this.emit('remote-added', name, url);
      return true;
    } catch (error) {
      console.error('Failed to add remote:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Dispose service
   */
  public dispose(): void {
    this.repositories.clear();
    this.removeAllListeners();
  }
}
