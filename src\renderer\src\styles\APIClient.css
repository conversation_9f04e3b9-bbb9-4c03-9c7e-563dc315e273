/* API Client Styles */

.api-client {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-primary);
}

.api-client-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  min-height: 60px;
}

.api-client-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.api-client-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.environment-selector {
  position: relative;
}

.environment-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--tertiary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
}

.environment-btn:hover {
  background: var(--accent-bg);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.environment-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1001;
  margin-top: 4px;
  overflow: hidden;
}

.environment-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
  color: var(--text-primary);
}

.environment-item:hover {
  background: var(--accent-bg);
}

.environment-item.active {
  background: var(--neon-cyan);
  color: var(--primary-bg);
}

.environment-divider {
  height: 1px;
  background: var(--border-secondary);
  margin: var(--spacing-xs) 0;
}

.api-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  background: var(--tertiary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.api-action-btn:hover {
  background: var(--accent-bg);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.api-action-btn.close-btn {
  background: var(--error);
  border-color: var(--error);
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.api-action-btn.close-btn:hover {
  background: #ff4444;
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
}

.api-client-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Sidebar */
.api-sidebar {
  width: 300px;
  background: var(--secondary-bg);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--tertiary-bg);
  border-bottom: 1px solid var(--border-secondary);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sidebar-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: var(--secondary-bg);
  color: var(--text-primary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.sidebar-action:hover {
  background: var(--accent-bg);
  color: var(--neon-cyan);
}

.collections-list {
  flex: 1;
  padding: var(--spacing-sm);
}

.collection-item {
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.collection-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--tertiary-bg);
  border-bottom: 1px solid var(--border-secondary);
}

.collection-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
}

.collection-name {
  font-size: 14px;
  font-weight: 500;
}

.collection-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: var(--secondary-bg);
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.collection-action:hover {
  background: var(--accent-bg);
  color: var(--neon-cyan);
}

.requests-list {
  background: var(--primary-bg);
}

.request-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
}

.request-item:hover {
  background: var(--tertiary-bg);
  border-left-color: var(--border-secondary);
}

.request-item.selected {
  background: var(--accent-bg);
  border-left-color: var(--neon-cyan);
  box-shadow: inset 0 0 0 1px rgba(0, 212, 255, 0.2);
}

.request-method {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 40px;
}

.request-name {
  font-size: 13px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.collections-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-muted);
  height: 100%;
}

.collections-empty svg {
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.collections-empty h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.collections-empty p {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Main Content */
.api-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.api-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-muted);
  height: 100%;
}

.api-empty svg {
  margin-bottom: var(--spacing-lg);
  color: var(--neon-cyan);
  opacity: 0.7;
}

.api-empty h2 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 24px;
  color: var(--text-primary);
}

.api-empty p {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 16px;
  line-height: 1.5;
  max-width: 400px;
}

/* Request Builder */
.request-builder {
  border-bottom: 1px solid var(--border-secondary);
}

.request-line {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-secondary);
}

.method-select {
  background: var(--tertiary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  min-width: 80px;
}

.method-select:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.url-input {
  flex: 1;
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-size: 14px;
}

.url-input:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.send-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  background: var(--neon-cyan);
  color: var(--primary-bg);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 600;
}

.send-btn:hover:not(:disabled) {
  background: #00b8e6;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
}

.send-btn:disabled {
  background: var(--tertiary-bg);
  color: var(--text-muted);
  cursor: not-allowed;
}

/* Request Tabs */
.request-tabs {
  background: var(--primary-bg);
}

.tab-headers {
  display: flex;
  border-bottom: 1px solid var(--border-secondary);
  background: var(--tertiary-bg);
}

.tab-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
  border-bottom: 2px solid transparent;
}

.tab-header:hover {
  color: var(--text-primary);
  background: var(--accent-bg);
}

.tab-header.active {
  color: var(--neon-cyan);
  border-bottom-color: var(--neon-cyan);
  background: var(--primary-bg);
}

.tab-count {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.tab-content {
  padding: var(--spacing-md);
  max-height: 300px;
  overflow-y: auto;
}

/* Parameters Tab */
.params-header,
.headers-header,
.body-header,
.auth-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.params-header h4,
.headers-header h4,
.body-header h4,
.auth-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.add-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  background: var(--tertiary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 12px;
}

.add-btn:hover {
  background: var(--accent-bg);
  border-color: var(--neon-cyan);
}

.param-row,
.header-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.param-row input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--neon-cyan);
}

.param-key,
.param-value,
.header-key,
.header-value {
  background: var(--secondary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--text-primary);
  font-size: 13px;
}

.param-key,
.header-key {
  flex: 1;
}

.param-value,
.header-value {
  flex: 2;
}

.param-key:focus,
.param-value:focus,
.header-key:focus,
.header-value:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 5px rgba(0, 212, 255, 0.2);
}

.remove-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: var(--error);
  color: white;
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.remove-btn:hover {
  background: #ff4444;
}

.params-empty {
  text-align: center;
  color: var(--text-muted);
  padding: var(--spacing-lg);
}

.params-empty p {
  margin: 0;
  font-size: 14px;
}

/* Body Tab */
.body-type-select {
  background: var(--tertiary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--text-primary);
  font-size: 13px;
  cursor: pointer;
}

.body-type-select:focus {
  outline: none;
  border-color: var(--neon-cyan);
}

.body-textarea {
  width: 100%;
  background: var(--secondary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 13px;
  font-family: 'Fira Code', monospace;
  resize: vertical;
  margin-top: var(--spacing-sm);
}

.body-textarea:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

/* Auth Tab */
.auth-type-select {
  background: var(--tertiary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--text-primary);
  font-size: 13px;
  cursor: pointer;
}

.auth-type-select:focus {
  outline: none;
  border-color: var(--neon-cyan);
}

.auth-fields {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.auth-input {
  background: var(--secondary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 13px;
}

.auth-input:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 5px rgba(0, 212, 255, 0.2);
}

/* Response Viewer */
.response-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-top: 1px solid var(--border-secondary);
}

.response-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-secondary);
}

.response-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.status-code {
  font-size: 16px;
  font-weight: 600;
}

.response-time,
.response-size {
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--tertiary-bg);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.response-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.response-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: var(--tertiary-bg);
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.response-action:hover {
  background: var(--accent-bg);
  color: var(--text-primary);
}

.response-body {
  flex: 1;
  overflow: auto;
  padding: var(--spacing-md);
}

.response-content {
  background: var(--secondary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  font-family: 'Fira Code', monospace;
  font-size: 13px;
  color: var(--text-primary);
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  overflow: auto;
}

.response-error {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid var(--error);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  color: var(--error);
}

.response-error h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 14px;
}

.response-error p {
  margin: 0;
  font-size: 13px;
}

.response-headers {
  padding: var(--spacing-md);
  overflow: auto;
}

.response-header-item {
  display: flex;
  margin-bottom: var(--spacing-xs);
  font-size: 13px;
  font-family: 'Fira Code', monospace;
}

.header-name {
  color: var(--text-secondary);
  min-width: 150px;
  font-weight: 500;
}

.header-value {
  color: var(--text-primary);
  word-break: break-all;
}

/* Custom scrollbars */
.api-sidebar::-webkit-scrollbar,
.tab-content::-webkit-scrollbar,
.response-body::-webkit-scrollbar,
.response-headers::-webkit-scrollbar,
.response-content::-webkit-scrollbar {
  width: 6px;
}

.api-sidebar::-webkit-scrollbar-track,
.tab-content::-webkit-scrollbar-track,
.response-body::-webkit-scrollbar-track,
.response-headers::-webkit-scrollbar-track,
.response-content::-webkit-scrollbar-track {
  background: var(--primary-bg);
}

.api-sidebar::-webkit-scrollbar-thumb,
.tab-content::-webkit-scrollbar-thumb,
.response-body::-webkit-scrollbar-thumb,
.response-headers::-webkit-scrollbar-thumb,
.response-content::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

.api-sidebar::-webkit-scrollbar-thumb:hover,
.tab-content::-webkit-scrollbar-thumb:hover,
.response-body::-webkit-scrollbar-thumb:hover,
.response-headers::-webkit-scrollbar-thumb:hover,
.response-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Responsive design */
@media (max-width: 1200px) {
  .api-sidebar {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .api-client-content {
    flex-direction: column;
  }
  
  .api-sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-primary);
  }
  
  .request-line {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .method-select {
    width: 100%;
  }
  
  .response-status {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}
